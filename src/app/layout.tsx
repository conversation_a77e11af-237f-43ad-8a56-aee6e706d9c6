import type { Metada<PERSON> } from "next";
import { Playfair_Display, Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import ErrorBoundary from "@/components/ui/error-boundary";
import { OrganizationStructuredData } from "@/components/seo/StructuredData";
import WebVitals from "@/components/performance/WebVitals";

const playfairDisplay = Playfair_Display({
  variable: "--font-heading",
  subsets: ["latin"],
  display: 'swap',
});

const inter = Inter({
  variable: "--font-body",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Garrisons Tours - Authentic Sri Lankan Experiences",
  description: "Discover the authentic beauty of Sri Lanka with personalized tour experiences. Cultural tours, wildlife safaris, adventure trips, and romantic getaways.",
  keywords: ["Sri Lanka tours", "Sri Lanka travel", "cultural tours", "wildlife safari", "adventure tours"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${playfairDisplay.variable} ${inter.variable} font-body antialiased min-h-screen flex flex-col`}
      >
        <OrganizationStructuredData />
        <WebVitals />
        <ErrorBoundary>
          <Header />
          <main className="flex-grow">
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </main>
          <Footer />
        </ErrorBoundary>
      </body>
    </html>
  );
}
