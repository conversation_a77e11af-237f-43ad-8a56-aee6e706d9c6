import mongoose, { Document, Schema } from 'mongoose';

export interface ITestimonial extends Document {
  customerName: string;
  customerEmail: string;
  customerCountry: string;
  customerPhoto?: string;
  tourId?: mongoose.Types.ObjectId;
  tourTitle?: string;
  rating: number;
  title: string;
  content: string;
  travelDate: Date;
  isApproved: boolean;
  isFeatured: boolean;
  submittedAt: Date;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const TestimonialSchema = new Schema<ITestimonial>({
  customerName: { type: String, required: true },
  customerEmail: { type: String, required: true },
  customerCountry: { type: String, required: true },
  customerPhoto: { type: String },
  tourId: { type: Schema.Types.ObjectId, ref: 'Tour' },
  tourTitle: { type: String },
  rating: { type: Number, required: true, min: 1, max: 5 },
  title: { type: String, required: true },
  content: { type: String, required: true },
  travelDate: { type: Date, required: true },
  isApproved: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  submittedAt: { type: Date, default: Date.now },
  approvedAt: { type: Date }
}, {
  timestamps: true
});

// Create indexes for better performance
TestimonialSchema.index({ isApproved: 1 });
TestimonialSchema.index({ isFeatured: 1 });
TestimonialSchema.index({ rating: -1 });
TestimonialSchema.index({ submittedAt: -1 });
TestimonialSchema.index({ tourId: 1 });

export default mongoose.models.Testimonial || mongoose.model<ITestimonial>('Testimonial', TestimonialSchema);
