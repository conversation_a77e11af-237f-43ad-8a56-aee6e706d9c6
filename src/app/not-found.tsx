import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Home, ArrowLeft, Search, MapPin } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-2xl w-full text-center">
        <Card className="shadow-lg">
          <CardContent className="pt-12 pb-8">
            {/* 404 Illustration */}
            <div className="mb-8">
              <div className="text-8xl font-bold text-emerald-600 mb-4">404</div>
              <div className="text-6xl mb-4">🏝️</div>
            </div>

            {/* Error Message */}
            <h1 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              Oops! Page Not Found
            </h1>
            <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
              It looks like you've wandered off the beaten path. The page you're looking for doesn't exist or has been moved.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button asChild size="lg" className="bg-emerald-600 hover:bg-emerald-700">
                <Link href="/">
                  <Home className="h-5 w-5 mr-2" />
                  Back to Home
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg">
                <Link href="/tours">
                  <MapPin className="h-5 w-5 mr-2" />
                  Explore Tours
                </Link>
              </Button>
            </div>

            {/* Helpful Links */}
            <div className="border-t pt-6">
              <p className="text-sm text-gray-500 mb-4">
                Looking for something specific? Try these popular pages:
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <Link href="/tours" className="text-emerald-600 hover:text-emerald-700 hover:underline">
                  Tour Packages
                </Link>
                <Link href="/about" className="text-emerald-600 hover:text-emerald-700 hover:underline">
                  About Us
                </Link>
                <Link href="/blog" className="text-emerald-600 hover:text-emerald-700 hover:underline">
                  Travel Blog
                </Link>
                <Link href="/gallery" className="text-emerald-600 hover:text-emerald-700 hover:underline">
                  Photo Gallery
                </Link>
                <Link href="/contact" className="text-emerald-600 hover:text-emerald-700 hover:underline">
                  Contact Us
                </Link>
              </div>
            </div>

            {/* Search Suggestion */}
            <div className="mt-6 p-4 bg-emerald-50 rounded-lg">
              <div className="flex items-center justify-center text-emerald-700">
                <Search className="h-5 w-5 mr-2" />
                <span className="text-sm">
                  Can't find what you're looking for? 
                  <Link href="/contact" className="font-medium hover:underline ml-1">
                    Contact our travel experts
                  </Link>
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Help */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            If you believe this is an error, please{' '}
            <Link href="/contact" className="text-emerald-600 hover:text-emerald-700 hover:underline">
              contact our support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
