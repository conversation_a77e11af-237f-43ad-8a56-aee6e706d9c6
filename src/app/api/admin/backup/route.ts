import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import connectDB from '@/lib/mongodb';

// Type for session user with role
interface SessionUser {
  role?: string;
  [key: string]: unknown;
}
import Tour from '@/models/Tour';
import BlogPost from '@/models/BlogPost';
import Testimonial from '@/models/Testimonial';
import User from '@/models/User';
import GalleryImage from '@/models/Gallery';
import BookingInquiry from '@/models/BookingInquiry';
import { createSecureResponse, createErrorResponse } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session || (session.user as SessionUser)?.role !== 'admin') {
      return createErrorResponse('Unauthorized', 401);
    }

    await connectDB();

    // Get all data from collections
    const [tours, blogPosts, testimonials, users, galleryImages, bookings] = await Promise.all([
      Tour.find({}).lean(),
      BlogPost.find({}).lean(),
      Testimonial.find({}).lean(),
      User.find({}).select('-password').lean(), // Exclude passwords
      GalleryImage.find({}).lean(),
      BookingInquiry.find({}).lean()
    ]);

    const backup = {
      metadata: {
        createdAt: new Date().toISOString(),
        version: '1.0',
        collections: {
          tours: tours.length,
          blogPosts: blogPosts.length,
          testimonials: testimonials.length,
          users: users.length,
          galleryImages: galleryImages.length,
          bookings: bookings.length
        }
      },
      data: {
        tours,
        blogPosts,
        testimonials,
        users,
        galleryImages,
        bookings
      }
    };

    // Set headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    headers.set('Content-Disposition', `attachment; filename="garrisons-tours-backup-${new Date().toISOString().split('T')[0]}.json"`);

    return new NextResponse(JSON.stringify(backup, null, 2), {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('Backup error:', error);
    return createErrorResponse('Failed to create backup', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session || (session.user as SessionUser)?.role !== 'admin') {
      return createErrorResponse('Unauthorized', 401);
    }

    const body = await request.json();
    const { data, options = {} } = body;

    if (!data || typeof data !== 'object') {
      return createErrorResponse('Invalid backup data', 400);
    }

    await connectDB();

    const results = {
      tours: 0,
      blogPosts: 0,
      testimonials: 0,
      galleryImages: 0,
      bookings: 0
    };

    // Restore tours
    if (data.tours && Array.isArray(data.tours)) {
      if (options.clearExisting) {
        await Tour.deleteMany({});
      }
      
      for (const tourData of data.tours) {
        delete tourData._id; // Remove _id to let MongoDB generate new ones
        await Tour.create(tourData);
        results.tours++;
      }
    }

    // Restore blog posts
    if (data.blogPosts && Array.isArray(data.blogPosts)) {
      if (options.clearExisting) {
        await BlogPost.deleteMany({});
      }
      
      for (const postData of data.blogPosts) {
        delete postData._id;
        await BlogPost.create(postData);
        results.blogPosts++;
      }
    }

    // Restore testimonials
    if (data.testimonials && Array.isArray(data.testimonials)) {
      if (options.clearExisting) {
        await Testimonial.deleteMany({});
      }
      
      for (const testimonialData of data.testimonials) {
        delete testimonialData._id;
        await Testimonial.create(testimonialData);
        results.testimonials++;
      }
    }

    // Restore gallery images
    if (data.galleryImages && Array.isArray(data.galleryImages)) {
      if (options.clearExisting) {
        await GalleryImage.deleteMany({});
      }
      
      for (const imageData of data.galleryImages) {
        delete imageData._id;
        await GalleryImage.create(imageData);
        results.galleryImages++;
      }
    }

    // Restore bookings (be careful with this in production)
    if (data.bookings && Array.isArray(data.bookings) && options.includeBookings) {
      if (options.clearExisting) {
        await BookingInquiry.deleteMany({});
      }
      
      for (const bookingData of data.bookings) {
        delete bookingData._id;
        await BookingInquiry.create(bookingData);
        results.bookings++;
      }
    }

    return createSecureResponse({
      success: true,
      message: 'Backup restored successfully',
      results
    });

  } catch (error) {
    console.error('Restore error:', error);
    return createErrorResponse('Failed to restore backup', 500);
  }
}
