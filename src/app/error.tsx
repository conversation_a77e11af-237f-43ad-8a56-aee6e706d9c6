'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, MessageCircle } from 'lucide-react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
    
    // In production, you would send this to an error monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry, LogRocket, Bugsnag, etc.
      // errorReportingService.captureException(error);
    }
  }, [error]);

  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-2xl w-full text-center">
        <Card className="shadow-lg">
          <CardContent className="pt-12 pb-8">
            {/* Error Icon */}
            <div className="mb-8">
              <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="h-10 w-10 text-red-600" />
              </div>
            </div>

            {/* Error Message */}
            <h1 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              Something went wrong!
            </h1>
            <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
              We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
            </p>

            {/* Development Error Details */}
            {isDevelopment && (
              <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg text-left">
                <h3 className="font-semibold text-red-800 mb-2">Development Error Details:</h3>
                <p className="text-sm text-red-700 font-mono break-all">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="text-xs text-red-600 mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Button 
                onClick={reset} 
                size="lg" 
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                <RefreshCw className="h-5 w-5 mr-2" />
                Try Again
              </Button>
              
              <Button asChild variant="outline" size="lg">
                <Link href="/">
                  <Home className="h-5 w-5 mr-2" />
                  Back to Home
                </Link>
              </Button>
            </div>

            {/* Help Section */}
            <div className="border-t pt-6">
              <p className="text-sm text-gray-500 mb-4">
                If the problem persists, you can:
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button asChild variant="ghost" size="sm">
                  <Link href="/contact">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Contact Support
                  </Link>
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Page
                </Button>
              </div>
            </div>

            {/* Error ID for Support */}
            {error.digest && (
              <div className="mt-6 p-3 bg-gray-100 rounded-lg">
                <p className="text-xs text-gray-600">
                  Error Reference: <span className="font-mono">{error.digest}</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Please include this reference when contacting support
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Information */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            Our team has been automatically notified of this issue.{' '}
            <Link href="/contact" className="text-emerald-600 hover:text-emerald-700 hover:underline">
              Contact us
            </Link>{' '}
            if you need immediate assistance.
          </p>
        </div>
      </div>
    </div>
  );
}
