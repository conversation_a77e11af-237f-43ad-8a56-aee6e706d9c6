import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
}

export function generateSEOMetadata({
  title = 'Garrisons Tours - Authentic Sri Lankan Adventures',
  description = 'Discover the beauty of Sri Lanka with Garrisons Tours. Premium cultural, wildlife, and adventure experiences across the Pearl of the Indian Ocean.',
  keywords = ['Sri Lanka tours', 'Ceylon travel', 'cultural tours', 'wildlife safari', 'adventure travel', 'authentic experiences'],
  image = '/images/hero-sri-lanka.jpg',
  url = 'https://garrisonstours.com',
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = []
}: SEOProps): Metadata {
  const siteName = 'Garrisons Tours';
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
  const fullUrl = url.startsWith('http') ? url : `https://garrisonstours.com${url}`;
  const fullImage = image.startsWith('http') ? image : `https://garrisonstours.com${image}`;

  return {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: author ? [{ name: author }] : [{ name: 'Garrisons Tours' }],
    creator: 'Garrisons Tours',
    publisher: 'Garrisons Tours',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://garrisonstours.com'),
    alternates: {
      canonical: fullUrl,
    },
    openGraph: {
      title: fullTitle,
      description,
      url: fullUrl,
      siteName,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: 'en_US',
      type: type as 'website' | 'article' | 'product',
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags }),
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullImage],
      creator: '@garrisonstours',
      site: '@garrisonstours',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
  };
}

// Predefined SEO configurations for different page types
export const seoConfigs = {
  homepage: {
    title: 'Garrisons Tours - Authentic Sri Lankan Adventures',
    description: 'Discover the beauty of Sri Lanka with Garrisons Tours. Premium cultural, wildlife, and adventure experiences across the Pearl of the Indian Ocean.',
    keywords: ['Sri Lanka tours', 'Ceylon travel', 'cultural tours', 'wildlife safari', 'adventure travel', 'authentic experiences', 'Sri Lankan holidays'],
    url: '/',
  },
  
  tours: {
    title: 'Sri Lanka Tour Packages - Cultural, Wildlife & Adventure Tours',
    description: 'Explore our carefully crafted Sri Lanka tour packages. From ancient temples to wildlife safaris, discover authentic experiences with expert local guides.',
    keywords: ['Sri Lanka tour packages', 'Ceylon tours', 'cultural triangle', 'wildlife safari', 'adventure tours', 'honeymoon packages'],
    url: '/tours',
  },
  
  blog: {
    title: 'Sri Lanka Travel Blog - Tips, Guides & Stories',
    description: 'Get insider tips and travel guides for Sri Lanka. Discover hidden gems, cultural insights, and practical advice from local experts.',
    keywords: ['Sri Lanka travel blog', 'travel tips', 'destination guides', 'cultural insights', 'travel stories'],
    url: '/blog',
  },
  
  gallery: {
    title: 'Sri Lanka Photo Gallery - Beautiful Destinations & Experiences',
    description: 'Browse our stunning photo gallery showcasing the natural beauty, cultural heritage, and wildlife of Sri Lanka.',
    keywords: ['Sri Lanka photos', 'travel photography', 'destinations', 'wildlife', 'cultural sites'],
    url: '/gallery',
  },
  
  testimonials: {
    title: 'Customer Reviews & Testimonials - Garrisons Tours',
    description: 'Read authentic reviews from travelers who experienced Sri Lanka with Garrisons Tours. See why we are rated as a top tour operator.',
    keywords: ['Sri Lanka tour reviews', 'customer testimonials', 'travel experiences', 'tour operator reviews'],
    url: '/testimonials',
  },
  
  about: {
    title: 'About Garrisons Tours - Your Sri Lankan Adventure Specialists',
    description: 'Learn about Garrisons Tours, your trusted partner for authentic Sri Lankan experiences. Meet our team and discover our commitment to sustainable tourism.',
    keywords: ['about Garrisons Tours', 'Sri Lanka tour operator', 'sustainable tourism', 'local expertise'],
    url: '/about',
  },
  
  contact: {
    title: 'Contact Garrisons Tours - Plan Your Sri Lankan Adventure',
    description: 'Get in touch with Garrisons Tours to plan your perfect Sri Lankan adventure. Contact our expert team for personalized tour recommendations.',
    keywords: ['contact Garrisons Tours', 'Sri Lanka tour planning', 'travel consultation', 'tour booking'],
    url: '/contact',
  },
};

// Helper function to generate tour-specific SEO
export function generateTourSEO(tour: Record<string, unknown>): SEOProps {
  return {
    title: `${tour.title} - Sri Lanka Tour Package`,
    description: tour.shortDescription || tour.description?.substring(0, 160),
    keywords: [
      'Sri Lanka tour',
      tour.category,
      tour.title.toLowerCase(),
      ...tour.highlights?.slice(0, 3) || [],
      `${tour.duration.days} day tour`,
    ],
    image: tour.images?.featured || '/images/default-tour.jpg',
    url: `/tours/${tour.slug}`,
    type: 'product',
  };
}

// Helper function to generate blog post SEO
export function generateBlogSEO(post: Record<string, unknown>): SEOProps {
  return {
    title: post.title,
    description: post.excerpt || post.content?.substring(0, 160),
    keywords: [
      ...post.category || [],
      ...post.tags || [],
      'Sri Lanka travel',
      'travel blog',
    ],
    image: post.featuredImage || '/images/default-blog.jpg',
    url: `/blog/${post.slug}`,
    type: 'article',
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    author: post.author?.name,
    section: post.category?.[0],
    tags: post.tags,
  };
}

// JSON-LD structured data generators
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'TravelAgency',
    name: 'Garrisons Tours',
    description: 'Premium Sri Lankan tour operator offering authentic cultural, wildlife, and adventure experiences.',
    url: 'https://garrisonstours.com',
    logo: 'https://garrisonstours.com/images/logo.png',
    image: 'https://garrisonstours.com/images/hero-sri-lanka.jpg',
    telephone: '+94-77-123-4567',
    email: '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '123 Galle Road',
      addressLocality: 'Colombo 03',
      addressCountry: 'LK',
      postalCode: '00300',
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: 6.9271,
      longitude: 79.8612,
    },
    openingHours: 'Mo-Su 08:00-20:00',
    priceRange: '$$',
    areaServed: {
      '@type': 'Country',
      name: 'Sri Lanka',
    },
    serviceType: 'Tour Operator',
    sameAs: [
      'https://www.facebook.com/garrisonstours',
      'https://www.instagram.com/garrisonstours',
      'https://www.twitter.com/garrisonstours',
      'https://www.linkedin.com/company/garrisonstours',
    ],
  };
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Garrisons Tours',
    url: 'https://garrisonstours.com',
    description: 'Authentic Sri Lankan adventures and cultural experiences',
    publisher: {
      '@type': 'Organization',
      name: 'Garrisons Tours',
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://garrisonstours.com/tours?search={search_term_string}',
      'query-input': 'required name=search_term_string',
    },
  };
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `https://garrisonstours.com${item.url}`,
    })),
  };
}
