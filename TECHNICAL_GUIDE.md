# Garrisons Tours - Technical Documentation

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 14 with App Router, React 18, TypeScript
- **Styling**: Tailwind CSS with custom components
- **Backend**: Next.js API Routes with MongoDB
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: NextAuth.js with credentials provider
- **Email**: Nodemailer with SMTP
- **Deployment**: Vercel (recommended) or self-hosted

### Project Structure
```
garrisons-tours/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (admin)/           # Admin routes group
│   │   ├── api/               # API endpoints
│   │   ├── blog/              # Blog pages
│   │   ├── tours/             # Tour pages
│   │   ├── gallery/           # Photo gallery
│   │   ├── testimonials/      # Customer reviews
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # Reusable components
│   │   ├── admin/             # Admin-specific components
│   │   ├── layout/            # Layout components
│   │   ├── seo/               # SEO components
│   │   └── ui/                # UI components
│   ├── lib/                   # Utility functions
│   │   ├── mongodb.ts         # Database connection
│   │   ├── auth.ts            # Authentication config
│   │   ├── email.ts           # Email utilities
│   │   └── utils.ts           # General utilities
│   ├── models/                # MongoDB schemas
│   │   ├── Tour.ts            # Tour model
│   │   ├── BookingInquiry.ts  # Booking model
│   │   ├── BlogPost.ts        # Blog post model
│   │   ├── Testimonial.ts     # Testimonial model
│   │   ├── User.ts            # User model
│   │   └── GalleryImage.ts    # Gallery image model
│   └── types/                 # TypeScript definitions
├── public/                    # Static assets
├── .env.local                 # Environment variables
├── package.json               # Dependencies
├── tailwind.config.js         # Tailwind configuration
├── next.config.js             # Next.js configuration
└── tsconfig.json              # TypeScript configuration
```

## Database Schema

### Tour Model
```typescript
interface Tour {
  _id: ObjectId;
  title: string;
  slug: string;
  shortDescription: string;
  description: string;
  category: 'cultural' | 'wildlife' | 'adventure' | 'romantic' | 'family';
  difficulty: 'easy' | 'moderate' | 'challenging';
  duration: {
    days: number;
    nights: number;
  };
  price: {
    amount: number;
    currency: string;
    priceType: 'per_person' | 'per_group' | 'per_couple';
  };
  maxGroupSize: number;
  highlights: string[];
  included: string[];
  excluded: string[];
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    activities: string[];
    accommodation?: string;
    meals: string[];
  }>;
  images: {
    featured: string;
    gallery: string[];
    alt: string[];
  };
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### BookingInquiry Model
```typescript
interface BookingInquiry {
  _id: ObjectId;
  tourId: ObjectId;
  tourTitle: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    country: string;
  };
  travelDetails: {
    startDate: Date;
    endDate: Date;
    groupSize: number;
    adults: number;
    children: number;
  };
  specialRequests: string;
  status: 'new' | 'contacted' | 'quoted' | 'confirmed' | 'cancelled';
  estimatedPrice?: number;
  adminNotes: string;
  submittedAt: Date;
  updatedAt: Date;
}
```

### User Model
```typescript
interface User {
  _id: ObjectId;
  name: string;
  email: string;
  password: string; // hashed
  role: 'admin' | 'editor';
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## API Architecture

### Authentication Middleware
```typescript
// lib/auth.ts
export async function getServerSession(req: NextRequest) {
  // NextAuth.js session handling
  // Role-based access control
  // JWT token validation
}
```

### API Route Structure
```
/api/
├── auth/                      # NextAuth.js endpoints
├── tours/                     # Public tour endpoints
│   ├── route.ts              # GET /api/tours
│   └── [slug]/
│       └── route.ts          # GET /api/tours/[slug]
├── bookings/
│   └── route.ts              # POST /api/bookings
├── blog/                     # Blog endpoints
├── testimonials/             # Testimonial endpoints
├── contact/                  # Contact form endpoint
└── admin/                    # Protected admin endpoints
    ├── tours/
    ├── bookings/
    ├── testimonials/
    └── users/
```

### Error Handling
```typescript
// Standardized error responses
export class APIError extends Error {
  statusCode: number;
  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
  }
}

// Usage in API routes
try {
  // API logic
} catch (error) {
  if (error instanceof APIError) {
    return NextResponse.json(
      { error: error.message },
      { status: error.statusCode }
    );
  }
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  );
}
```

## Component Architecture

### UI Components
Built with Tailwind CSS and following atomic design principles:

```typescript
// components/ui/button.tsx
interface ButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
}
```

### Layout Components
```typescript
// components/layout/Header.tsx
- Navigation menu
- Mobile responsive
- Authentication state

// components/layout/Footer.tsx
- Company information
- Quick links
- Social media links
```

### Admin Components
```typescript
// components/admin/AdminLayout.tsx
- Sidebar navigation
- User menu
- Breadcrumbs
- Role-based rendering
```

## State Management

### Client-Side State
- React hooks for local component state
- Context API for shared state (authentication)
- No external state management library (Redux, Zustand) needed

### Server State
- Next.js API routes for data fetching
- MongoDB for persistent storage
- Session-based authentication state

## SEO Implementation

### Structured Data
```typescript
// components/seo/StructuredData.tsx
- Organization schema
- Tour/Product schema
- Blog post schema
- Review schema
```

### Meta Tags
```typescript
// Dynamic meta tags per page
export const metadata: Metadata = {
  title: 'Page Title',
  description: 'Page description',
  openGraph: {
    title: 'OG Title',
    description: 'OG Description',
    images: ['image-url'],
  },
};
```

### Sitemap Generation
```typescript
// app/sitemap.ts
- Dynamic sitemap generation
- Includes all tours and blog posts
- Proper lastModified dates
- Search engine optimization
```

## Performance Optimization

### Image Optimization
```typescript
// components/ui/optimized-image.tsx
- Next.js Image component
- Automatic WebP conversion
- Lazy loading
- Responsive sizing
- Blur placeholders
```

### Code Splitting
- Automatic code splitting with Next.js
- Dynamic imports for heavy components
- Route-based splitting

### Caching Strategy
```typescript
// API routes with caching
export async function GET() {
  return NextResponse.json(data, {
    headers: {
      'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
    }
  });
}
```

## Security Implementation

### Input Validation
```typescript
// lib/validation.ts
import { z } from 'zod';

export const tourSchema = z.object({
  title: z.string().min(1).max(200),
  price: z.number().positive(),
  // ... other validations
});
```

### Authentication Security
- Secure session management with NextAuth.js
- Password hashing with bcrypt
- CSRF protection
- Role-based access control

### Database Security
- MongoDB connection with authentication
- Input sanitization to prevent injection
- Proper error handling to avoid information leakage

## Email System

### Configuration
```typescript
// lib/email.ts
import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});
```

### Email Templates
- Booking confirmation emails
- Contact form notifications
- Admin notification emails
- HTML and text versions

## Testing Strategy

### Unit Testing
```bash
# Install testing dependencies
npm install --save-dev jest @testing-library/react @testing-library/jest-dom

# Run tests
npm test
```

### Integration Testing
- API endpoint testing
- Database integration tests
- Authentication flow testing

### E2E Testing
```bash
# Install Playwright
npm install --save-dev @playwright/test

# Run E2E tests
npm run test:e2e
```

## Monitoring & Analytics

### Performance Monitoring
```typescript
// components/analytics/Analytics.tsx
- Web Vitals tracking
- Custom performance metrics
- Error boundary reporting
```

### Business Analytics
- Google Analytics integration
- Conversion tracking
- User behavior analysis
- Booking funnel analysis

## Development Workflow

### Local Development
```bash
# Start development server
npm run dev

# Type checking
npm run type-check

# Linting
npm run lint

# Build for production
npm run build
```

### Git Workflow
```bash
# Feature branch workflow
git checkout -b feature/new-feature
git commit -m "Add new feature"
git push origin feature/new-feature
# Create pull request
```

### Code Quality
- ESLint configuration
- Prettier formatting
- TypeScript strict mode
- Pre-commit hooks with Husky

## Deployment Configuration

### Next.js Configuration
```javascript
// next.config.js
const nextConfig = {
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    serverActions: true,
  },
};
```

### Environment-Specific Settings
```typescript
// Different configs for development/production
const config = {
  development: {
    apiUrl: 'http://localhost:3000',
    dbUrl: 'mongodb://localhost:27017/garrisons-tours-dev',
  },
  production: {
    apiUrl: 'https://garrisonstours.com',
    dbUrl: process.env.MONGODB_URI,
  },
};
```

## Scaling Considerations

### Database Scaling
- MongoDB Atlas auto-scaling
- Read replicas for high traffic
- Proper indexing strategy
- Connection pooling

### Application Scaling
- Vercel automatic scaling
- CDN for static assets
- Image optimization service
- API rate limiting

### Monitoring & Alerting
- Application performance monitoring
- Database performance monitoring
- Error tracking and alerting
- Uptime monitoring

---

This technical documentation provides the foundation for understanding, maintaining, and extending the Garrisons Tours application. For specific implementation details, refer to the code comments and inline documentation.
