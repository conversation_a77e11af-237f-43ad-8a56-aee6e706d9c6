import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BookingInquiry from '@/models/BookingInquiry';
import Tour from '@/models/Tour';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    
    // Validate required fields
    const { tourId, customer, travelDetails } = body;
    
    if (!tourId || !customer?.name || !customer?.email || !customer?.phone || 
        !travelDetails?.startDate || !travelDetails?.endDate || !travelDetails?.groupSize) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get tour details
    const tour = await Tour.findById(tourId);
    if (!tour) {
      return NextResponse.json(
        { error: 'Tour not found' },
        { status: 404 }
      );
    }

    // Create booking inquiry
    const bookingInquiry = new BookingInquiry({
      ...body,
      tourTitle: tour.title,
      submittedAt: new Date()
    });

    await bookingInquiry.save();

    // TODO: Send email notifications here
    // - Send confirmation email to customer
    // - Send notification email to admin

    return NextResponse.json({
      success: true,
      inquiryId: bookingInquiry._id,
      message: 'Booking inquiry submitted successfully. We will contact you within 24 hours.'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating booking inquiry:', error);
    return NextResponse.json(
      { error: 'Failed to submit booking inquiry' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, string> = {};
    if (status && status !== 'all') {
      query.status = status;
    }

    // Get booking inquiries with pagination
    const bookings = await BookingInquiry.find(query)
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await BookingInquiry.countDocuments(query);

    return NextResponse.json({
      bookings,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: skip + bookings.length < total
    });

  } catch (error) {
    console.error('Error fetching booking inquiries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch booking inquiries' },
      { status: 500 }
    );
  }
}