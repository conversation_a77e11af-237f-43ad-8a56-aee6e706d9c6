import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - Garrisons Tours',
  description: 'Read the terms and conditions for using Garrisons Tours services and booking our Sri Lankan travel experiences.',
  robots: 'index, follow',
};

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-64 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
              Terms of Service
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto px-4">
              Terms and conditions for using our services
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-600 mb-6">
              <strong>Last updated:</strong> January 1, 2024
            </p>

            <h2>Acceptance of Terms</h2>
            <p>
              By accessing and using Garrisons Tours services, you accept and agree to be bound by 
              the terms and provision of this agreement.
            </p>

            <h2>Booking and Payment</h2>
            <h3>Booking Process</h3>
            <ul>
              <li>All bookings are subject to availability</li>
              <li>A deposit is required to confirm your booking</li>
              <li>Full payment is due as specified in your booking confirmation</li>
            </ul>

            <h3>Pricing</h3>
            <ul>
              <li>All prices are quoted in USD unless otherwise specified</li>
              <li>Prices are subject to change without notice until booking is confirmed</li>
              <li>Additional costs may apply for special requests or changes</li>
            </ul>

            <h2>Cancellation Policy</h2>
            <ul>
              <li><strong>30+ days before departure:</strong> Full refund minus processing fee</li>
              <li><strong>15-29 days before departure:</strong> 50% refund</li>
              <li><strong>Less than 15 days:</strong> No refund</li>
              <li>Cancellations due to force majeure events will be handled case by case</li>
            </ul>

            <h2>Travel Requirements</h2>
            <ul>
              <li>Valid passport required for all international travelers</li>
              <li>Visa requirements are the responsibility of the traveler</li>
              <li>Travel insurance is strongly recommended</li>
              <li>Health and vaccination requirements as per Sri Lankan regulations</li>
            </ul>

            <h2>Liability</h2>
            <p>
              Garrisons Tours acts as an agent for various service providers. We are not liable for 
              acts, errors, omissions, representations, warranties, breaches, or negligence of such 
              service providers.
            </p>

            <h2>Force Majeure</h2>
            <p>
              We are not responsible for any failure to perform due to unforeseen circumstances 
              beyond our reasonable control, including but not limited to natural disasters, 
              government actions, or travel restrictions.
            </p>

            <h2>Dispute Resolution</h2>
            <p>
              Any disputes arising from these terms will be resolved through arbitration in 
              accordance with Sri Lankan law.
            </p>

            <h2>Contact Information</h2>
            <p>
              For questions about these Terms of Service, contact us at:
            </p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Phone: +94 77 123 4567</li>
              <li>Address: 123 Galle Road, Colombo 03, Sri Lanka</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
