import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - Garrisons Tours | Plan Your Sri Lankan Adventure',
  description: 'Contact Garrisons Tours to plan your perfect Sri Lankan adventure. Get in touch via phone, email, or our contact form. We respond within 24 hours.',
  keywords: ['contact garrisons tours', 'sri lanka tour booking', 'travel inquiry', 'tour consultation', 'sri lanka travel agent'],
  openGraph: {
    title: 'Contact Garrisons Tours - Plan Your Sri Lankan Adventure',
    description: 'Ready to explore Sri Lanka? Contact our expert team to plan your perfect adventure. Phone, email, or contact form - we\'re here to help!',
    type: 'website',
    url: 'https://garrisonstours.com/contact',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Garrisons Tours - Sri Lankan Travel Experts',
    description: 'Get in touch with our Sri Lankan travel experts to plan your perfect adventure. We respond within 24 hours.',
  }
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
