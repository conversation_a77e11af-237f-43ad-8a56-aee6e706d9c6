'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Calendar, Users, Mail, Phone, User, MessageSquare, Send } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';

interface BookingFormProps {
  tourId: string;
  tourTitle: string;
  tourPrice: number;
  className?: string;
}

interface FormData {
  tourId: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    country: string;
  };
  travelDetails: {
    startDate: Date | undefined;
    endDate: Date | undefined;
    groupSize: number;
    adults: number;
    children: number;
  };
  specialRequests: string;
}

export default function BookingForm({ tourId, tourTitle, tourPrice, className }: BookingFormProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const [formData, setFormData] = useState<FormData>({
    tourId,
    customer: {
      name: '',
      email: '',
      phone: '',
      country: ''
    },
    travelDetails: {
      startDate: undefined,
      endDate: undefined,
      groupSize: 2,
      adults: 2,
      children: 0
    },
    specialRequests: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.customer.name.trim()) {
        newErrors.name = 'Name is required';
      }
      if (!formData.customer.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.customer.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
      if (!formData.customer.phone.trim()) {
        newErrors.phone = 'Phone number is required';
      }
      if (!formData.customer.country.trim()) {
        newErrors.country = 'Country is required';
      }
    }

    if (step === 2) {
      if (!formData.travelDetails.startDate) {
        newErrors.startDate = 'Start date is required';
      }
      if (!formData.travelDetails.endDate) {
        newErrors.endDate = 'End date is required';
      }
      if (formData.travelDetails.startDate && formData.travelDetails.endDate) {
        if (formData.travelDetails.startDate >= formData.travelDetails.endDate) {
          newErrors.endDate = 'End date must be after start date';
        }
      }
      if (formData.travelDetails.adults < 1) {
        newErrors.adults = 'At least 1 adult is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
    setErrors({});
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        // Redirect to thank you page with booking reference
        router.push(`/booking/thank-you?ref=${data.inquiryId}`);
      } else {
        setSubmitStatus('error');
        setErrorMessage(data.error || 'Failed to submit booking inquiry');
      }
    } catch (error) {
      console.error('Error submitting booking:', error);
      setSubmitStatus('error');
      setErrorMessage('Failed to submit booking inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (section: keyof FormData, field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section] as Record<string, unknown>,
        [field]: value
      }
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const updateTravelDetails = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      travelDetails: {
        ...prev.travelDetails,
        [field]: value,
        groupSize: field === 'adults' || field === 'children' 
          ? (field === 'adults' ? Number(value) : prev.travelDetails.adults) + 
            (field === 'children' ? Number(value) : prev.travelDetails.children)
          : prev.travelDetails.groupSize
      }
    }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
    'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland',
    'Austria', 'Belgium', 'Italy', 'Spain', 'Japan', 'South Korea', 'Singapore',
    'New Zealand', 'Ireland', 'Other'
  ];

  return (
    <Card className={className} id="booking">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-emerald-600" />
          Book Your Adventure
        </CardTitle>
        <p className="text-gray-600">
          Ready to experience {tourTitle}? Fill out the form below and we'll get back to you within 24 hours.
        </p>
      </CardHeader>
      <CardContent>
        {/* Progress Indicator */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? 'bg-emerald-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={`w-12 h-0.5 ${
                    step < currentStep ? 'bg-emerald-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.customer.name}
                  onChange={(e) => updateFormData('customer', 'name', e.target.value)}
                  placeholder="Your full name"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>
              
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.customer.email}
                  onChange={(e) => updateFormData('customer', 'email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.customer.phone}
                  onChange={(e) => updateFormData('customer', 'phone', e.target.value)}
                  placeholder="+****************"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
              </div>
              
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select 
                  value={formData.customer.country} 
                  onValueChange={(value) => updateFormData('customer', 'country', value)}
                >
                  <SelectTrigger className={errors.country ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.country && <p className="text-red-500 text-sm mt-1">{errors.country}</p>}
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={handleNext} className="bg-emerald-600 hover:bg-emerald-700">
                Next Step
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Travel Details */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Travel Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Preferred Start Date *</Label>
                <DatePicker
                  date={formData.travelDetails.startDate}
                  onDateChange={(date) => updateTravelDetails('startDate', date)}
                  placeholder="Select start date"
                  className={errors.startDate ? 'border-red-500' : ''}
                />
                {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
              </div>
              
              <div>
                <Label>Preferred End Date *</Label>
                <DatePicker
                  date={formData.travelDetails.endDate}
                  onDateChange={(date) => updateTravelDetails('endDate', date)}
                  placeholder="Select end date"
                  className={errors.endDate ? 'border-red-500' : ''}
                />
                {errors.endDate && <p className="text-red-500 text-sm mt-1">{errors.endDate}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="adults">Number of Adults *</Label>
                <Select 
                  value={formData.travelDetails.adults.toString()} 
                  onValueChange={(value) => updateTravelDetails('adults', parseInt(value))}
                >
                  <SelectTrigger className={errors.adults ? 'border-red-500' : ''}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} Adult{num > 1 ? 's' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.adults && <p className="text-red-500 text-sm mt-1">{errors.adults}</p>}
              </div>
              
              <div>
                <Label htmlFor="children">Number of Children</Label>
                <Select 
                  value={formData.travelDetails.children.toString()} 
                  onValueChange={(value) => updateTravelDetails('children', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[0, 1, 2, 3, 4, 5].map((num) => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {num === 1 ? 'Child' : 'Children'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="font-medium">Total Group Size:</span>
                <span className="text-lg font-bold text-emerald-600">
                  {formData.travelDetails.groupSize} People
                </span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-gray-600">Estimated Total:</span>
                <span className="text-lg font-bold">
                  ${(tourPrice * formData.travelDetails.adults).toLocaleString()}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                *Final price may vary based on accommodation and specific requirements
              </p>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevious}>
                Previous
              </Button>
              <Button onClick={handleNext} className="bg-emerald-600 hover:bg-emerald-700">
                Next Step
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Special Requests & Review */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Special Requests & Review
            </h3>
            
            <div>
              <Label htmlFor="specialRequests">Special Requests or Questions</Label>
              <Textarea
                id="specialRequests"
                value={formData.specialRequests}
                onChange={(e) => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
                placeholder="Any dietary restrictions, accessibility needs, special occasions, or other requests..."
                rows={4}
              />
            </div>

            {/* Booking Summary */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h4 className="font-semibold mb-4">Booking Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Tour:</span>
                  <span className="font-medium">{tourTitle}</span>
                </div>
                <div className="flex justify-between">
                  <span>Traveler:</span>
                  <span>{formData.customer.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Email:</span>
                  <span>{formData.customer.email}</span>
                </div>
                <div className="flex justify-between">
                  <span>Group Size:</span>
                  <span>{formData.travelDetails.adults} Adults, {formData.travelDetails.children} Children</span>
                </div>
                <div className="flex justify-between">
                  <span>Travel Dates:</span>
                  <span>
                    {formData.travelDetails.startDate?.toLocaleDateString()} - {formData.travelDetails.endDate?.toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between font-semibold text-base pt-2 border-t">
                  <span>Estimated Total:</span>
                  <span>${(tourPrice * formData.travelDetails.adults).toLocaleString()}</span>
                </div>
              </div>
            </div>

            {submitStatus === 'error' && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800">{errorMessage}</p>
              </div>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevious} disabled={isSubmitting}>
                Previous
              </Button>
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Inquiry
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
