'use client';

import { useEffect } from 'react';

// Web Vitals metric interface
interface WebVitalMetric {
  name: string;
  value: number;
  id: string;
  delta: number;
  entries: PerformanceEntry[];
}

// Web Vitals monitoring component
export default function WebVitals() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Import web-vitals dynamically to avoid SSR issues
    import('web-vitals').then((webVitals) => {
      // Function to send metrics to analytics
      const sendToAnalytics = (metric: WebVitalMetric) => {
        // Send to Google Analytics if available
        if (window.gtag) {
          window.gtag('event', metric.name, {
            event_category: 'Web Vitals',
            value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
            event_label: metric.id,
            non_interaction: true,
          });
        }

        // Send to console in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Web Vital:', metric);
        }

        // You can also send to other analytics services here
        // Example: Vercel Analytics, Mixpanel, etc.
      };

      // Measure all Web Vitals (check if functions exist)
      if (webVitals.onCLS) webVitals.onCLS(sendToAnalytics);
      if (webVitals.onFCP) webVitals.onFCP(sendToAnalytics);
      if (webVitals.onLCP) webVitals.onLCP(sendToAnalytics);
      if (webVitals.onTTFB) webVitals.onTTFB(sendToAnalytics);
      if (webVitals.onINP) webVitals.onINP(sendToAnalytics); // INP replaces FID in newer versions
    });

    // Custom performance metrics
    const measureCustomMetrics = () => {
      // Measure page load time
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        const pageLoadTime = timing.loadEventEnd - timing.navigationStart;
        
        if (pageLoadTime > 0 && window.gtag) {
          window.gtag('event', 'page_load_time', {
            event_category: 'Performance',
            value: pageLoadTime,
            non_interaction: true,
          });
        }
      }

      // Measure DOM content loaded time
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        const domContentLoadedTime = timing.domContentLoadedEventEnd - timing.navigationStart;
        
        if (domContentLoadedTime > 0 && window.gtag) {
          window.gtag('event', 'dom_content_loaded', {
            event_category: 'Performance',
            value: domContentLoadedTime,
            non_interaction: true,
          });
        }
      }
    };

    // Measure custom metrics after page load
    if (document.readyState === 'complete') {
      measureCustomMetrics();
    } else {
      window.addEventListener('load', measureCustomMetrics);
    }

    // Performance observer for additional metrics
    if ('PerformanceObserver' in window) {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            
            // DNS lookup time
            const dnsTime = navEntry.domainLookupEnd - navEntry.domainLookupStart;
            if (dnsTime > 0 && window.gtag) {
              window.gtag('event', 'dns_lookup_time', {
                event_category: 'Performance',
                value: Math.round(dnsTime),
                non_interaction: true,
              });
            }

            // Connection time
            const connectionTime = navEntry.connectEnd - navEntry.connectStart;
            if (connectionTime > 0 && window.gtag) {
              window.gtag('event', 'connection_time', {
                event_category: 'Performance',
                value: Math.round(connectionTime),
                non_interaction: true,
              });
            }

            // Server response time
            const responseTime = navEntry.responseEnd - navEntry.requestStart;
            if (responseTime > 0 && window.gtag) {
              window.gtag('event', 'server_response_time', {
                event_category: 'Performance',
                value: Math.round(responseTime),
                non_interaction: true,
              });
            }
          }
        }
      });

      navObserver.observe({ entryTypes: ['navigation'] });

      // Observe resource timing for slow resources
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const resourceEntry = entry as PerformanceResourceTiming;
          const loadTime = resourceEntry.responseEnd - resourceEntry.startTime;
          
          // Report slow resources (> 1 second)
          if (loadTime > 1000 && window.gtag) {
            window.gtag('event', 'slow_resource', {
              event_category: 'Performance',
              event_label: resourceEntry.name,
              value: Math.round(loadTime),
              non_interaction: true,
            });
          }
        }
      });

      resourceObserver.observe({ entryTypes: ['resource'] });

      // Clean up observers
      return () => {
        navObserver.disconnect();
        resourceObserver.disconnect();
      };
    }
  }, []);

  return null; // This component doesn't render anything
}

// Hook for tracking custom performance metrics
export function usePerformanceTracking() {
  const trackCustomMetric = (name: string, value: number, category = 'Custom') => {
    if (window.gtag) {
      window.gtag('event', name, {
        event_category: category,
        value: Math.round(value),
        non_interaction: true,
      });
    }
  };

  const trackUserTiming = (name: string, startTime?: number) => {
    if (!window.performance) return;

    if (startTime) {
      const duration = performance.now() - startTime;
      trackCustomMetric(name, duration, 'User Timing');
    } else {
      return performance.now();
    }
  };

  const trackResourceLoad = (resourceName: string, startTime: number) => {
    const duration = performance.now() - startTime;
    trackCustomMetric(`resource_load_${resourceName}`, duration, 'Resource Loading');
  };

  return {
    trackCustomMetric,
    trackUserTiming,
    trackResourceLoad,
  };
}

// Component for measuring component render time
export function withPerformanceTracking<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  componentName: string
) {
  return function PerformanceTrackedComponent(props: T) {
    useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const renderTime = performance.now() - startTime;
        if (window.gtag) {
          window.gtag('event', 'component_render_time', {
            event_category: 'Performance',
            event_label: componentName,
            value: Math.round(renderTime),
            non_interaction: true,
          });
        }
      };
    }, []);

    return <WrappedComponent {...props} />;
  };
}

// Declare global gtag function
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js',
      targetId: string | Date,
      config?: Record<string, unknown>
    ) => void;
  }
}
