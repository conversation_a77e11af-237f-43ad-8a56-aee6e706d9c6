import mongoose, { Document, Schema } from 'mongoose';

export interface IBlogPost extends Document {
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  author: {
    name: string;
    avatar?: string;
  };
  category: string[];
  tags: string[];
  relatedTours: mongoose.Types.ObjectId[];
  isPublished: boolean;
  publishedAt?: Date;
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

const BlogPostSchema = new Schema<IBlogPost>({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  excerpt: { type: String, required: true },
  content: { type: String, required: true },
  featuredImage: { type: String, required: true },
  author: {
    name: { type: String, required: true },
    avatar: { type: String }
  },
  category: [{ type: String }],
  tags: [{ type: String }],
  relatedTours: [{ type: Schema.Types.ObjectId, ref: 'Tour' }],
  isPublished: { type: Boolean, default: false },
  publishedAt: { type: Date },
  seoMeta: {
    title: { type: String },
    description: { type: String },
    keywords: [{ type: String }]
  }
}, {
  timestamps: true
});

// Create indexes for better performance
BlogPostSchema.index({ slug: 1 });
BlogPostSchema.index({ isPublished: 1 });
BlogPostSchema.index({ publishedAt: -1 });
BlogPostSchema.index({ category: 1 });
BlogPostSchema.index({ tags: 1 });

export default mongoose.models.BlogPost || mongoose.model<IBlogPost>('BlogPost', BlogPostSchema);