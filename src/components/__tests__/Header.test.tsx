import { render, screen, fireEvent } from '@testing-library/react'
import Header from '@/components/layout/Header'

// Interface for navigation items
interface NavigationItem {
  name: string;
  href: string;
  [key: string]: unknown;
}

// Mock the MobileMenu component
jest.mock('@/components/layout/MobileMenu', () => {
  return function MockMobileMenu({ navigation }: { navigation: NavigationItem[] }) {
    return (
      <div data-testid="mobile-menu">
        {navigation.map((item) => (
          <a key={item.name} href={item.href}>
            {item.name}
          </a>
        ))}
      </div>
    )
  }
})

describe('Header Component', () => {
  it('renders the logo', () => {
    render(<Header />)
    expect(screen.getByText('Garrisons Tours')).toBeInTheDocument()
  })

  it('renders contact information', () => {
    render(<Header />)
    expect(screen.getByText('+94 77 123 4567')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('renders navigation links on desktop', () => {
    render(<Header />)
    
    // Check if navigation links are present
    expect(screen.getByRole('link', { name: /home/<USER>
    expect(screen.getByRole('link', { name: /tours/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /about/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /blog/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /gallery/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /contact/i })).toBeInTheDocument()
  })

  it('renders Book Now button', () => {
    render(<Header />)
    expect(screen.getByRole('link', { name: /book now/i })).toBeInTheDocument()
  })

  it('renders mobile menu component', () => {
    render(<Header />)
    expect(screen.getByTestId('mobile-menu')).toBeInTheDocument()
  })

  it('has correct navigation links', () => {
    render(<Header />)
    
    const homeLink = screen.getByRole('link', { name: /home/<USER>
    const toursLink = screen.getByRole('link', { name: /tours/i })
    const aboutLink = screen.getByRole('link', { name: /about/i })
    
    expect(homeLink).toHaveAttribute('href', '/')
    expect(toursLink).toHaveAttribute('href', '/tours')
    expect(aboutLink).toHaveAttribute('href', '/about')
  })

  it('has sticky positioning', () => {
    render(<Header />)
    const header = screen.getByRole('banner')
    expect(header).toHaveClass('sticky', 'top-0', 'z-50')
  })

  it('displays tagline on larger screens', () => {
    render(<Header />)
    expect(screen.getByText('Authentic Sri Lankan Experiences')).toBeInTheDocument()
  })
})
