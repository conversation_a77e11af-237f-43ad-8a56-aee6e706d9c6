'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, Users, Filter, Search, MapPin } from 'lucide-react';
import { Loading } from '@/components/ui/loading';

interface Tour {
  _id: string;
  title: string;
  slug: string;
  shortDescription: string;
  price: {
    amount: number;
    currency: string;
    priceType: string;
  };
  duration: {
    days: number;
    nights: number;
  };
  category: string;
  difficulty: string;
  maxGroupSize: number;
  images: {
    featured: string;
    gallery: string[];
  };
}

const categories = [
  { value: 'all', label: 'All Categories' },
  { value: 'cultural', label: 'Cultural Tours' },
  { value: 'wildlife', label: 'Wildlife Safari' },
  { value: 'adventure', label: 'Adventure Tours' },
  { value: 'romantic', label: 'Romantic Getaways' },
  { value: 'family', label: 'Family Tours' }
];

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'easy': return 'text-green-600 bg-green-100';
    case 'moderate': return 'text-yellow-600 bg-yellow-100';
    case 'challenging': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'cultural': return 'from-amber-400 to-amber-600';
    case 'wildlife': return 'from-green-400 to-green-600';
    case 'adventure': return 'from-blue-400 to-blue-600';
    case 'romantic': return 'from-pink-400 to-pink-600';
    case 'family': return 'from-purple-400 to-purple-600';
    default: return 'from-gray-400 to-gray-600';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'cultural': return '🏛️';
    case 'wildlife': return '🐘';
    case 'adventure': return '🏔️';
    case 'romantic': return '💕';
    case 'family': return '👨‍👩‍👧‍👦';
    default: return '🌴';
  }
};

export default function ToursPage() {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'cultural', label: 'Cultural Tours' },
    { value: 'wildlife', label: 'Wildlife Safari' },
    { value: 'adventure', label: 'Adventure Tours' },
    { value: 'romantic', label: 'Romantic Getaways' },
    { value: 'family', label: 'Family Tours' }
  ];

  useEffect(() => {
    fetchTours();
  }, [selectedCategory, currentPage]);

  const fetchTours = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '9'
      });

      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }

      const response = await fetch(`/api/tours?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch tours');
      }

      const data = await response.json();
      setTours(data.tours);
      setTotalPages(data.totalPages);
      setError(null);
    } catch (err) {
      setError('Failed to load tours. Please try again.');
      console.error('Error fetching tours:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredTours = tours.filter(tour =>
    tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tour.shortDescription.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-64 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
              Our Tour Packages
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto px-4">
              Discover Sri Lanka through our carefully curated experiences
            </p>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Filter & Search Tours:</span>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full lg:w-auto">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search tours..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full sm:w-64"
                />
              </div>

              {/* Category Filter */}
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Tours Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <Loading text="Loading tours..." className="py-20" />
          ) : error ? (
            <div className="text-center py-20">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={fetchTours} variant="outline">
                Try Again
              </Button>
            </div>
          ) : filteredTours.length === 0 ? (
            <div className="text-center py-20">
              <p className="text-gray-600 mb-4">No tours found matching your criteria.</p>
              <Button onClick={() => { setSearchTerm(''); setSelectedCategory('all'); }} variant="outline">
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredTours.map((tour) => (
              <Card key={tour._id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className={`relative h-48 bg-gradient-to-br ${getCategoryColor(tour.category)} flex items-center justify-center`}>
                  <div className="text-center text-white">
                    <div className="text-4xl mb-2">{getCategoryIcon(tour.category)}</div>
                    <p className="font-medium">{tour.location}</p>
                  </div>
                  <div className="absolute top-4 left-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getDifficultyColor(tour.difficulty)}`}>
                      {tour.difficulty}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-semibold text-emerald-600">
                    ${tour.price.amount}
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="mb-2">
                    <span className="inline-block px-2 py-1 bg-emerald-100 text-emerald-700 text-xs font-medium rounded-full capitalize">
                      {tour.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{tour.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {tour.shortDescription}
                  </p>
                  <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{tour.duration.days} Days</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      <span>Max {tour.maxGroupSize}</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button asChild className="flex-1 bg-emerald-600 hover:bg-emerald-700">
                      <Link href={`/tours/${tour.slug}`}>View Details</Link>
                    </Button>
                    <Button asChild variant="outline" className="flex-1">
                      <Link href={`/tours/${tour.slug}#booking`}>Book Now</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {!loading && !error && filteredTours.length > 0 && totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-12">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-heading font-bold mb-4">
            Can&apos;t Find What You&apos;re Looking For?
          </h2>
          <p className="text-xl mb-8 text-emerald-100">
            We specialize in creating custom itineraries tailored to your interests and budget.
          </p>
          <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-gray-100">
            <Link href="/contact">Create Custom Tour</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}