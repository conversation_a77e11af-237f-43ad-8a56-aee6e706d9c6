'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Star,
  Quote,
  Calendar,
  MapPin,
  Plus
} from 'lucide-react';
import { Loading } from '@/components/ui/loading';

interface Testimonial {
  _id: string;
  customerName: string;
  customerCountry: string;
  customerPhoto?: string;
  tourId?: {
    _id: string;
    title: string;
    slug: string;
  };
  tourTitle?: string;
  rating: number;
  title: string;
  content: string;
  travelDate: string;
  isFeatured: boolean;
  submittedAt: string;
}

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [featuredFilter, setFeaturedFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchTestimonials();
  }, [ratingFilter, featuredFilter, currentPage]);

  const fetchTestimonials = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        approved: 'true'
      });

      if (featuredFilter === 'featured') {
        params.append('featured', 'true');
      }

      const response = await fetch(`/api/testimonials?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch testimonials');
      }

      const data = await response.json();
      setTestimonials(data.testimonials);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('Error fetching testimonials:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTestimonials = testimonials.filter(testimonial => {
    const matchesSearch = testimonial.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.customerCountry.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRating = ratingFilter === 'all' || testimonial.rating === parseInt(ratingFilter);

    return matchesSearch && matchesRating;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-64 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
              Customer Testimonials
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto px-4">
              Read what our travelers say about their Sri Lankan adventures
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Filters */}
        <div className="mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search testimonials..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={ratingFilter} onValueChange={setRatingFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Rating" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Ratings</SelectItem>
                      <SelectItem value="5">5 Stars</SelectItem>
                      <SelectItem value="4">4 Stars</SelectItem>
                      <SelectItem value="3">3 Stars</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={featuredFilter} onValueChange={setFeaturedFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Reviews</SelectItem>
                      <SelectItem value="featured">Featured</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button asChild className="bg-emerald-600 hover:bg-emerald-700">
                    <Link href="/testimonials/submit">
                      <Plus className="h-4 w-4 mr-2" />
                      Share Your Story
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Testimonials Grid */}
        {loading ? (
          <Loading text="Loading testimonials..." className="py-20" />
        ) : filteredTestimonials.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTestimonials.map((testimonial) => (
                <Card key={testimonial._id} className="hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center text-white font-semibold">
                          {testimonial.customerPhoto ? (
                            <img
                              src={testimonial.customerPhoto}
                              alt={testimonial.customerName}
                              className="w-full h-full rounded-full object-cover"
                            />
                          ) : (
                            getInitials(testimonial.customerName)
                          )}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {testimonial.customerName}
                          </h3>
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {testimonial.customerCountry}
                          </p>
                        </div>
                      </div>
                      {testimonial.isFeatured && (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          Featured
                        </Badge>
                      )}
                    </div>

                    {/* Rating */}
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex">
                        {renderStars(testimonial.rating)}
                      </div>
                      <span className="text-sm text-gray-600">
                        {testimonial.rating}/5
                      </span>
                    </div>

                    {/* Title */}
                    <h4 className="font-semibold text-lg mb-3 text-gray-900">
                      {testimonial.title}
                    </h4>

                    {/* Content */}
                    <div className="relative mb-4">
                      <Quote className="absolute -top-2 -left-2 h-6 w-6 text-emerald-200" />
                      <p className="text-gray-600 leading-relaxed pl-4">
                        {testimonial.content}
                      </p>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>Traveled {formatDate(testimonial.travelDate)}</span>
                      </div>
                      {testimonial.tourId && (
                        <Link 
                          href={`/tours/${testimonial.tourId.slug}`}
                          className="text-emerald-600 hover:text-emerald-700 font-medium"
                        >
                          View Tour
                        </Link>
                      )}
                    </div>

                    {/* Tour Info */}
                    {(testimonial.tourTitle || testimonial.tourId) && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-600">
                          <strong>Tour:</strong> {testimonial.tourTitle || testimonial.tourId?.title}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-4 mt-12">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">⭐</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No testimonials found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || ratingFilter !== 'all' || featuredFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Be the first to share your travel experience'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {(searchTerm || ratingFilter !== 'all' || featuredFilter !== 'all') && (
                <Button 
                  variant="outline" 
                  onClick={() => { 
                    setSearchTerm(''); 
                    setRatingFilter('all'); 
                    setFeaturedFilter('all'); 
                  }}
                >
                  Clear Filters
                </Button>
              )}
              <Button asChild className="bg-emerald-600 hover:bg-emerald-700">
                <Link href="/testimonials/submit">
                  <Plus className="h-4 w-4 mr-2" />
                  Share Your Story
                </Link>
              </Button>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-16">
          <Card className="bg-emerald-50 border-emerald-200">
            <CardContent className="pt-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Share Your Sri Lankan Adventure
                </h2>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Have you traveled with us? We'd love to hear about your experience! 
                  Your story could inspire other travelers to discover the beauty of Sri Lanka.
                </p>
                <Button asChild size="lg" className="bg-emerald-600 hover:bg-emerald-700">
                  <Link href="/testimonials/submit">
                    Write Your Testimonial
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
