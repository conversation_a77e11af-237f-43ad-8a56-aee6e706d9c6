'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

// Touch-friendly button component
interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  touchFeedback?: boolean;
}

export function TouchButton({ 
  children, 
  className, 
  variant = 'default', 
  size = 'md',
  touchFeedback = true,
  ...props 
}: TouchButtonProps) {
  const [isPressed, setIsPressed] = useState(false);

  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none select-none";
  
  const variantClasses = {
    default: "bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500",
    outline: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-emerald-500",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-emerald-500"
  };

  const sizeClasses = {
    sm: "px-3 py-2 text-sm rounded-md min-h-[36px]", // Minimum 36px for touch
    md: "px-4 py-2 text-base rounded-md min-h-[44px]", // Minimum 44px for touch
    lg: "px-6 py-3 text-lg rounded-lg min-h-[48px]" // Minimum 48px for touch
  };

  const pressedClasses = touchFeedback && isPressed ? "scale-95 opacity-90" : "";

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        pressedClasses,
        className
      )}
      onTouchStart={() => touchFeedback && setIsPressed(true)}
      onTouchEnd={() => touchFeedback && setIsPressed(false)}
      onTouchCancel={() => touchFeedback && setIsPressed(false)}
      onMouseDown={() => touchFeedback && setIsPressed(true)}
      onMouseUp={() => touchFeedback && setIsPressed(false)}
      onMouseLeave={() => touchFeedback && setIsPressed(false)}
      {...props}
    >
      {children}
    </button>
  );
}

// Touch-friendly card component
interface TouchCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  pressable?: boolean;
}

export function TouchCard({ 
  children, 
  className, 
  onClick, 
  hoverable = true, 
  pressable = true 
}: TouchCardProps) {
  const [isPressed, setIsPressed] = useState(false);

  const baseClasses = "bg-white rounded-lg shadow-sm border transition-all duration-200";
  const hoverClasses = hoverable ? "hover:shadow-md hover:-translate-y-1" : "";
  const clickableClasses = onClick ? "cursor-pointer select-none" : "";
  const pressedClasses = pressable && isPressed ? "scale-98 shadow-sm" : "";

  return (
    <div
      className={cn(baseClasses, hoverClasses, clickableClasses, pressedClasses, className)}
      onClick={onClick}
      onTouchStart={() => pressable && onClick && setIsPressed(true)}
      onTouchEnd={() => pressable && onClick && setIsPressed(false)}
      onTouchCancel={() => pressable && onClick && setIsPressed(false)}
      onMouseDown={() => pressable && onClick && setIsPressed(true)}
      onMouseUp={() => pressable && onClick && setIsPressed(false)}
      onMouseLeave={() => pressable && onClick && setIsPressed(false)}
    >
      {children}
    </div>
  );
}

// Swipeable carousel component
interface SwipeableCarouselProps {
  children: React.ReactNode[];
  className?: string;
  showDots?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export function SwipeableCarousel({ 
  children, 
  className, 
  showDots = true, 
  autoPlay = false,
  autoPlayInterval = 5000 
}: SwipeableCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [startX, setStartX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % children.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, children.length]);

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    e.preventDefault();
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const endX = e.changedTouches[0].clientX;
    const diffX = startX - endX;
    const threshold = 50;

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0 && currentIndex < children.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (diffX < 0 && currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
    }

    setIsDragging(false);
  };

  const handleMouseStart = (e: React.MouseEvent) => {
    setStartX(e.clientX);
    setIsDragging(true);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    e.preventDefault();
  };

  const handleMouseEnd = (e: React.MouseEvent) => {
    if (!isDragging) return;
    
    const endX = e.clientX;
    const diffX = startX - endX;
    const threshold = 50;

    if (Math.abs(diffX) > threshold) {
      if (diffX > 0 && currentIndex < children.length - 1) {
        setCurrentIndex(currentIndex + 1);
      } else if (diffX < 0 && currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
    }

    setIsDragging(false);
  };

  return (
    <div className={cn("relative overflow-hidden", className)}>
      <div
        ref={containerRef}
        className="flex transition-transform duration-300 ease-out cursor-grab active:cursor-grabbing"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseStart}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseEnd}
        onMouseLeave={() => setIsDragging(false)}
      >
        {children.map((child, index) => (
          <div key={index} className="w-full flex-shrink-0">
            {child}
          </div>
        ))}
      </div>

      {showDots && (
        <div className="flex justify-center space-x-2 mt-4">
          {children.map((_, index) => (
            <button
              key={index}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center",
                index === currentIndex ? "bg-emerald-600" : "bg-gray-300"
              )}
              onClick={() => setCurrentIndex(index)}
            >
              <span className="sr-only">Go to slide {index + 1}</span>
              <div className={cn(
                "w-2 h-2 rounded-full",
                index === currentIndex ? "bg-white" : "bg-gray-600"
              )} />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Pull-to-refresh component
interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  className?: string;
  threshold?: number;
}

export function PullToRefresh({ 
  children, 
  onRefresh, 
  className, 
  threshold = 80 
}: PullToRefreshProps) {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (window.scrollY > 0 || isRefreshing) return;

    const currentY = e.touches[0].clientY;
    const distance = Math.max(0, currentY - startY);
    
    if (distance > 0) {
      e.preventDefault();
      setPullDistance(Math.min(distance, threshold * 1.5));
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
    setPullDistance(0);
  };

  const pullProgress = Math.min(pullDistance / threshold, 1);

  return (
    <div 
      ref={containerRef}
      className={cn("relative", className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull indicator */}
      <div 
        className="absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-200 bg-emerald-50"
        style={{ 
          height: `${pullDistance}px`,
          opacity: pullProgress 
        }}
      >
        <div className="text-emerald-600 text-sm font-medium">
          {isRefreshing ? 'Refreshing...' : pullDistance >= threshold ? 'Release to refresh' : 'Pull to refresh'}
        </div>
      </div>

      {/* Content */}
      <div 
        className="transition-transform duration-200"
        style={{ transform: `translateY(${pullDistance}px)` }}
      >
        {children}
      </div>
    </div>
  );
}

// Mobile-optimized input component
interface TouchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export function TouchInput({ 
  label, 
  error, 
  icon, 
  className, 
  ...props 
}: TouchInputProps) {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
        <input
          className={cn(
            "block w-full px-3 py-3 text-base border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 min-h-[44px]",
            icon && "pl-10",
            error && "border-red-300 focus:ring-red-500 focus:border-red-500",
            className
          )}
          {...props}
        />
      </div>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
