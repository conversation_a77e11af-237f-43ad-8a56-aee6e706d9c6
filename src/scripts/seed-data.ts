// Load environment variables before any imports
import { config } from 'dotenv';
config({ path: '.env.local' });

import connectDB from '../lib/mongodb';
import Tour from '../models/Tour';
import BlogPost from '../models/BlogPost';
import Testimonial from '../models/Testimonial';
import User from '../models/User';
import GalleryImage from '../models/Gallery';
import { hashPassword } from '../lib/security';

// Sample tour data
const sampleTours = [
  {
    title: 'Cultural Triangle Explorer',
    slug: 'cultural-triangle-explorer',
    shortDescription: 'Discover ancient temples, royal palaces, and UNESCO World Heritage sites in Sri Lanka\'s Cultural Triangle.',
    description: 'Embark on a fascinating journey through Sri Lanka\'s Cultural Triangle, home to ancient kingdoms and magnificent temples. Visit the sacred city of Anuradhapura, the rock fortress of Sigiriya, and the medieval capital of Polonnaruwa. This tour combines history, culture, and spirituality in one unforgettable experience.',
    category: 'cultural',
    difficulty: 'moderate',
    duration: { days: 7, nights: 6 },
    price: { amount: 899, currency: 'USD', priceType: 'per_person' },
    maxGroupSize: 12,
    highlights: [
      'Climb the iconic Sigiriya Rock Fortress',
      'Explore ancient ruins of Anuradhapura',
      'Visit the Temple of the Tooth in Kandy',
      'Experience traditional Kandyan dance',
      'Discover Polonnaruwa archaeological site'
    ],
    included: [
      'Accommodation in 4-star hotels',
      'All meals as specified',
      'Professional English-speaking guide',
      'Air-conditioned transportation',
      'Entrance fees to all attractions',
      'Cultural show tickets'
    ],
    excluded: [
      'International flights',
      'Visa fees',
      'Personal expenses',
      'Tips and gratuities',
      'Travel insurance'
    ],
    itinerary: [
      {
        day: 1,
        title: 'Arrival in Colombo',
        description: 'Welcome to Sri Lanka! Transfer to your hotel and evening briefing.',
        activities: ['Airport pickup', 'Hotel check-in', 'Welcome dinner'],
        accommodation: 'Colombo City Hotel',
        meals: ['dinner']
      },
      {
        day: 2,
        title: 'Colombo to Anuradhapura',
        description: 'Journey to the ancient capital and explore sacred sites.',
        activities: ['Sri Maha Bodhi Tree', 'Ruwanwelisaya Dagoba', 'Jetavanaramaya'],
        accommodation: 'Heritage Hotel Anuradhapura',
        meals: ['breakfast', 'lunch', 'dinner']
      },
      {
        day: 3,
        title: 'Anuradhapura to Sigiriya',
        description: 'Visit more ancient sites and travel to Sigiriya.',
        activities: ['Abhayagiri Monastery', 'Isurumuniya Temple', 'Travel to Sigiriya'],
        accommodation: 'Sigiriya Village Hotel',
        meals: ['breakfast', 'lunch', 'dinner']
      }
    ],
    images: {
      featured: '/images/tours/cultural-triangle-featured.jpg',
      gallery: [
        '/images/tours/cultural-triangle-1.jpg',
        '/images/tours/cultural-triangle-2.jpg',
        '/images/tours/cultural-triangle-3.jpg'
      ],
      alt: ['Sigiriya Rock Fortress', 'Ancient Buddha statue', 'Temple complex']
    },
    seoMeta: {
      title: 'Cultural Triangle Explorer - 7 Day Sri Lanka Heritage Tour',
      description: 'Explore Sri Lanka\'s Cultural Triangle with visits to Sigiriya, Anuradhapura, and Kandy. 7-day guided tour with accommodation and meals included.',
      keywords: ['Sri Lanka cultural tour', 'Sigiriya', 'Anuradhapura', 'Cultural Triangle', 'heritage tour']
    },
    isActive: true
  },
  {
    title: 'Wildlife Safari Adventure',
    slug: 'wildlife-safari-adventure',
    shortDescription: 'Experience Sri Lanka\'s incredible wildlife with safaris in Yala and Udawalawe National Parks.',
    description: 'Get up close with Sri Lanka\'s amazing wildlife on this thrilling safari adventure. Spot leopards, elephants, and exotic birds in their natural habitat across multiple national parks.',
    category: 'wildlife',
    difficulty: 'easy',
    duration: { days: 5, nights: 4 },
    price: { amount: 699, currency: 'USD', priceType: 'per_person' },
    maxGroupSize: 8,
    highlights: [
      'Safari in Yala National Park',
      'Elephant watching at Udawalawe',
      'Bird watching at Bundala',
      'Visit elephant orphanage',
      'Leopard spotting opportunities'
    ],
    included: [
      'Safari jeep with driver',
      'Park entrance fees',
      'Professional naturalist guide',
      'Accommodation in safari lodges',
      'All meals during safari',
      'Binoculars and field guides'
    ],
    excluded: [
      'Transportation to/from Colombo',
      'Personal expenses',
      'Alcoholic beverages',
      'Tips for guides and drivers',
      'Travel insurance'
    ],
    itinerary: [
      {
        day: 1,
        title: 'Arrival and Yala National Park',
        description: 'Arrive and enjoy your first safari experience.',
        activities: ['Check-in at safari lodge', 'Afternoon safari in Yala', 'Wildlife briefing'],
        accommodation: 'Yala Safari Lodge',
        meals: ['lunch', 'dinner']
      }
    ],
    images: {
      featured: '/images/tours/wildlife-safari-featured.jpg',
      gallery: [
        '/images/tours/wildlife-safari-1.jpg',
        '/images/tours/wildlife-safari-2.jpg'
      ],
      alt: ['Sri Lankan leopard', 'Elephant herd']
    },
    seoMeta: {
      title: 'Wildlife Safari Adventure - 5 Day Sri Lanka Safari Tour',
      description: 'Experience Sri Lanka\'s wildlife with safaris in Yala and Udawalawe National Parks. See leopards, elephants, and exotic birds.',
      keywords: ['Sri Lanka safari', 'Yala National Park', 'wildlife tour', 'leopard safari', 'elephant watching']
    },
    isActive: true
  }
];

// Sample blog posts
const sampleBlogPosts = [
  {
    title: 'Top 10 Must-Visit Places in Sri Lanka',
    slug: 'top-10-must-visit-places-sri-lanka',
    excerpt: 'Discover the most breathtaking destinations in the Pearl of the Indian Ocean, from ancient temples to pristine beaches.',
    content: `Sri Lanka, the Pearl of the Indian Ocean, is a treasure trove of natural beauty, rich culture, and ancient history. Here are the top 10 places you absolutely must visit:

1. **Sigiriya Rock Fortress** - This ancient rock citadel is one of Sri Lanka's most iconic landmarks.

2. **Temple of the Tooth, Kandy** - A sacred Buddhist temple housing a relic of Buddha's tooth.

3. **Galle Fort** - A UNESCO World Heritage site showcasing Dutch colonial architecture.

4. **Yala National Park** - Home to leopards, elephants, and diverse wildlife.

5. **Ella** - A charming hill station with stunning views and hiking trails.

6. **Anuradhapura** - Ancient capital with magnificent dagobas and ruins.

7. **Mirissa Beach** - Perfect for whale watching and relaxing on golden sands.

8. **Adam's Peak** - Sacred mountain with breathtaking sunrise views.

9. **Polonnaruwa** - Medieval capital with well-preserved archaeological sites.

10. **Nuwara Eliya** - Hill country town known for tea plantations and cool climate.

Each destination offers unique experiences that showcase Sri Lanka's incredible diversity.`,
    author: {
      name: 'Sarah Johnson',
      avatar: '/images/authors/sarah-johnson.jpg'
    },
    category: ['destinations', 'travel-tips'],
    tags: ['Sri Lanka', 'travel guide', 'destinations', 'must-visit'],
    featuredImage: '/images/blog/top-10-places-featured.jpg',
    isPublished: true,
    publishedAt: new Date('2024-01-15'),
    seoMeta: {
      title: 'Top 10 Must-Visit Places in Sri Lanka - Complete Travel Guide',
      description: 'Discover the most breathtaking destinations in Sri Lanka including Sigiriya, Kandy, Galle Fort, and more. Complete travel guide with tips.',
      keywords: ['Sri Lanka destinations', 'places to visit Sri Lanka', 'Sri Lanka travel guide', 'top attractions Sri Lanka']
    }
  },
  {
    title: 'Sri Lankan Cuisine: A Food Lover\'s Paradise',
    slug: 'sri-lankan-cuisine-food-lovers-paradise',
    excerpt: 'Explore the rich flavors and spices of Sri Lankan cuisine, from street food to traditional curries.',
    content: `Sri Lankan cuisine is a delightful fusion of flavors, influenced by Indian, Dutch, Portuguese, and British culinary traditions. The island's tropical climate provides an abundance of fresh ingredients and aromatic spices.

**Must-Try Dishes:**

- **Rice and Curry** - The staple meal with various curries and accompaniments
- **Hoppers (Appa)** - Bowl-shaped pancakes perfect for breakfast
- **Kottu Roti** - Stir-fried chopped roti with vegetables and meat
- **Fish Ambul Thiyal** - Sour fish curry from the southern provinces
- **Watalappan** - Traditional dessert made with coconut milk and jaggery

**Spices and Ingredients:**
Sri Lankan cooking features unique spice blends including cinnamon, cardamom, cloves, and curry leaves. Coconut in various forms is essential to most dishes.

**Where to Eat:**
From street food stalls to fine dining restaurants, Sri Lanka offers culinary experiences for every budget and preference.`,
    author: {
      name: 'Rajesh Perera',
      avatar: '/images/authors/rajesh-perera.jpg'
    },
    category: ['food', 'culture'],
    tags: ['Sri Lankan food', 'cuisine', 'spices', 'traditional food'],
    featuredImage: '/images/blog/sri-lankan-cuisine-featured.jpg',
    isPublished: true,
    publishedAt: new Date('2024-01-20'),
    seoMeta: {
      title: 'Sri Lankan Cuisine Guide - Traditional Food and Spices',
      description: 'Discover authentic Sri Lankan cuisine, from rice and curry to hoppers. Complete guide to traditional dishes, spices, and where to eat.',
      keywords: ['Sri Lankan food', 'Sri Lankan cuisine', 'traditional dishes', 'spices', 'food guide']
    }
  }
];

// Sample testimonials
const sampleTestimonials = [
  {
    customerName: 'Emily Watson',
    customerEmail: '<EMAIL>',
    customerCountry: 'United Kingdom',
    customerPhoto: '/images/testimonials/emily-watson.jpg',
    tourTitle: 'Cultural Triangle Explorer',
    rating: 5,
    title: 'Absolutely Amazing Experience!',
    content: 'Our trip to Sri Lanka with Garrisons Tours exceeded all expectations. The Cultural Triangle tour was perfectly organized, and our guide was incredibly knowledgeable. Climbing Sigiriya at sunrise was unforgettable!',
    travelDate: new Date('2023-12-10'),
    isApproved: true,
    isFeatured: true,
    submittedAt: new Date('2023-12-20')
  },
  {
    customerName: 'Michael Chen',
    customerEmail: '<EMAIL>',
    customerCountry: 'Australia',
    tourTitle: 'Wildlife Safari Adventure',
    rating: 5,
    title: 'Wildlife Paradise',
    content: 'The wildlife safari was incredible! We saw leopards, elephants, and so many bird species. The accommodation was comfortable and the food was delicious. Highly recommend Garrisons Tours!',
    travelDate: new Date('2024-01-05'),
    isApproved: true,
    isFeatured: true,
    submittedAt: new Date('2024-01-15')
  },
  {
    customerName: 'Sophie Martin',
    customerEmail: '<EMAIL>',
    customerCountry: 'France',
    tourTitle: 'Cultural Triangle Explorer',
    rating: 4,
    title: 'Great Cultural Experience',
    content: 'Wonderful introduction to Sri Lankan culture and history. The temples were magnificent and our guide shared fascinating stories. Only minor issue was the long travel times between sites.',
    travelDate: new Date('2023-11-15'),
    isApproved: true,
    isFeatured: false,
    submittedAt: new Date('2023-11-25')
  }
];

// Sample gallery images
const sampleGalleryImages = [
  {
    title: 'Sigiriya Rock Fortress at Sunset',
    description: 'The iconic Sigiriya Rock Fortress illuminated by golden sunset light.',
    imageUrl: '/images/gallery/sigiriya-sunset.jpg',
    thumbnailUrl: '/images/gallery/thumbs/sigiriya-sunset-thumb.jpg',
    category: 'destinations',
    location: 'Sigiriya, Central Province',
    tags: ['Sigiriya', 'sunset', 'fortress', 'UNESCO'],
    isActive: true,
    sortOrder: 1
  },
  {
    title: 'Sri Lankan Leopard in Yala',
    description: 'A magnificent Sri Lankan leopard spotted during a safari in Yala National Park.',
    imageUrl: '/images/gallery/yala-leopard.jpg',
    thumbnailUrl: '/images/gallery/thumbs/yala-leopard-thumb.jpg',
    category: 'wildlife',
    location: 'Yala National Park',
    tags: ['leopard', 'wildlife', 'safari', 'Yala'],
    isActive: true,
    sortOrder: 2
  },
  {
    title: 'Traditional Kandyan Dancers',
    description: 'Colorful traditional Kandyan dancers performing at a cultural show.',
    imageUrl: '/images/gallery/kandyan-dancers.jpg',
    thumbnailUrl: '/images/gallery/thumbs/kandyan-dancers-thumb.jpg',
    category: 'culture',
    location: 'Kandy, Central Province',
    tags: ['dance', 'culture', 'traditional', 'Kandy'],
    isActive: true,
    sortOrder: 3
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Connected to database');

    // Clear existing data (optional - comment out for production)
    console.log('🧹 Clearing existing data...');
    await Tour.deleteMany({});
    await BlogPost.deleteMany({});
    await Testimonial.deleteMany({});
    await GalleryImage.deleteMany({});
    // Don't clear users in production
    if (process.env.NODE_ENV !== 'production') {
      await User.deleteMany({});
    }

    // Create admin user
    console.log('👤 Creating admin user...');
    const adminPassword = await hashPassword(process.env.ADMIN_PASSWORD || 'admin123');
    const adminUser = new User({
      name: 'Admin User',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: adminPassword,
      role: 'admin',
      isActive: true
    });
    await adminUser.save();
    console.log('✅ Admin user created');

    // Seed tours
    console.log('🗺️ Seeding tours...');
    for (const tourData of sampleTours) {
      const tour = new Tour(tourData);
      await tour.save();
    }
    console.log(`✅ Created ${sampleTours.length} tours`);

    // Seed blog posts
    console.log('📝 Seeding blog posts...');
    for (const postData of sampleBlogPosts) {
      const post = new BlogPost(postData);
      await post.save();
    }
    console.log(`✅ Created ${sampleBlogPosts.length} blog posts`);

    // Seed testimonials
    console.log('⭐ Seeding testimonials...');
    for (const testimonialData of sampleTestimonials) {
      const testimonial = new Testimonial(testimonialData);
      await testimonial.save();
    }
    console.log(`✅ Created ${sampleTestimonials.length} testimonials`);

    // Seed gallery images
    console.log('🖼️ Seeding gallery images...');
    for (const imageData of sampleGalleryImages) {
      const image = new GalleryImage({
        ...imageData,
        uploadedBy: adminUser._id
      });
      await image.save();
    }
    console.log(`✅ Created ${sampleGalleryImages.length} gallery images`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Admin user: ${adminUser.email}`);
    console.log(`- Tours: ${sampleTours.length}`);
    console.log(`- Blog posts: ${sampleBlogPosts.length}`);
    console.log(`- Testimonials: ${sampleTestimonials.length}`);
    console.log(`- Gallery images: ${sampleGalleryImages.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seedDatabase;
