import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Testimonial from '@/models/Testimonial';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const approved = searchParams.get('approved') !== 'false'; // Default to approved only
    const featured = searchParams.get('featured');
    const tourId = searchParams.get('tourId');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, any> = {};
    if (approved) {
      query.isApproved = true;
    }
    if (featured === 'true') {
      query.isFeatured = true;
    }
    if (tourId) {
      query.tourId = tourId;
    }

    // Get testimonials with pagination
    const testimonials = await Testimonial.find(query)
      .sort({ isFeatured: -1, rating: -1, submittedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('tourId', 'title slug')
      .lean();

    // Get total count for pagination
    const total = await Testimonial.countDocuments(query);

    return NextResponse.json({
      testimonials,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: skip + testimonials.length < total
    });

  } catch (error) {
    console.error('Error fetching testimonials:', error);
    return NextResponse.json(
      { error: 'Failed to fetch testimonials' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    
    // Validate required fields
    const { customerName, customerEmail, customerCountry, rating, title, content, travelDate } = body;
    
    if (!customerName || !customerEmail || !customerCountry || !rating || !title || !content || !travelDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create new testimonial
    const testimonial = new Testimonial({
      ...body,
      submittedAt: new Date()
    });
    
    await testimonial.save();

    return NextResponse.json({
      success: true,
      message: 'Thank you for your testimonial! It will be reviewed and published soon.',
      testimonialId: testimonial._id
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating testimonial:', error);
    return NextResponse.json(
      { error: 'Failed to submit testimonial' },
      { status: 500 }
    );
  }
}
