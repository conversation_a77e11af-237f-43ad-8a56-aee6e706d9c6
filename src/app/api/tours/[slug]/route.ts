import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Tour, { ITour } from '@/models/Tour';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    await connectDB();
    const { slug } = await context.params;

    const tour = await Tour.findOne({ 
      slug: slug, 
      isActive: true 
    }).lean();

    if (!tour) {
      return NextResponse.json(
        { error: 'Tour not found' },
        { status: 404 }
      );
    }

    // Get related tours (same category, excluding current tour)
    const tourData = tour as unknown as ITour;
    const relatedTours = await Tour.find({
      category: tourData.category,
      _id: { $ne: tourData._id },
      isActive: true
    })
      .select('title slug shortDescription images price duration')
      .limit(3)
      .lean();

    return NextResponse.json({
      tour,
      relatedTours
    });

  } catch (error) {
    console.error('Error fetching tour:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tour' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    await connectDB();
    const { slug } = await context.params;
    const body = await request.json();

    // Update tour
    const updatedTour = await Tour.findOneAndUpdate(
      { slug },
      body,
      { new: true, runValidators: true }
    );

    if (!updatedTour) {
      return NextResponse.json(
        { error: 'Tour not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedTour);

  } catch (error) {
    console.error('Error updating tour:', error);
    return NextResponse.json(
      { error: 'Failed to update tour' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) {
  try {
    await connectDB();
    const { slug } = await context.params;

    // Instead of deleting, mark as inactive to preserve booking inquiries
    const updatedTour = await Tour.findOneAndUpdate(
      { slug },
      { isActive: false },
      { new: true }
    );

    if (!updatedTour) {
      return NextResponse.json(
        { error: 'Tour not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Tour deactivated successfully',
      tour: updatedTour
    });

  } catch (error) {
    console.error('Error deactivating tour:', error);
    return NextResponse.json(
      { error: 'Failed to deactivate tour' },
      { status: 500 }
    );
  }
}