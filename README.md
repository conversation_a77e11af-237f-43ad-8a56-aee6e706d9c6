# Garrisons Tours - Sri Lankan Tour Operator Website

A comprehensive, modern web application for Garrisons Tours, a premium Sri Lankan tour operator. Built with Next.js 14, TypeScript, and MongoDB, featuring a full-featured admin panel, booking system, and content management.

## 🌟 Features

### Public Website
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Tour Packages**: Detailed tour listings with filtering and search
- **Booking System**: Multi-step booking inquiry form with email notifications
- **Blog System**: Travel blog with categories and SEO optimization
- **Photo Gallery**: Lightbox gallery with categorization
- **Customer Testimonials**: Review system with ratings and approval workflow
- **Contact Forms**: Multiple contact points with form validation
- **SEO Optimized**: Structured data, sitemaps, and meta tags

### Admin Panel
- **Dashboard**: Analytics and overview of bookings, tours, and testimonials
- **Tour Management**: Create, edit, and manage tour packages
- **Booking Management**: Handle customer inquiries and track status
- **Content Management**: Blog posts, testimonials, and gallery management
- **User Management**: Admin and editor roles with authentication
- **Analytics Integration**: Performance monitoring and tracking

### Technical Features
- **Next.js 14**: App Router, Server Components, and API Routes
- **TypeScript**: Full type safety throughout the application
- **MongoDB**: Flexible document database with Mongoose ODM
- **Authentication**: NextAuth.js with role-based access control
- **Email System**: Nodemailer integration for notifications
- **Image Optimization**: Next.js Image component with lazy loading
- **Performance**: Web Vitals monitoring and optimization
- **Security**: Input validation, CSRF protection, and secure headers

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- MongoDB database
- SMTP email service (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/garrisons-tours.git
   cd garrisons-tours
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Configure the variables in `.env.local`:
   ```env
   # Database
   MONGODB_URI=mongodb://localhost:27017/garrisons-tours

   # Authentication
   NEXTAUTH_SECRET=your-secret-key
   NEXTAUTH_URL=http://localhost:3000

   # Admin Credentials
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123

   # Email Configuration (Optional)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Public website: http://localhost:3000
   - Admin panel: http://localhost:3000/admin (<EMAIL> / admin123)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin routes
│   ├── api/               # API endpoints
│   ├── blog/              # Blog pages
│   ├── tours/             # Tour pages
│   └── ...                # Other pages
├── components/            # Reusable components
│   ├── admin/             # Admin-specific components
│   ├── layout/            # Layout components
│   ├── seo/               # SEO components
│   └── ui/                # UI components
├── lib/                   # Utility functions
├── models/                # MongoDB models
└── types/                 # TypeScript types
```

## 🛠️ Key Features Implemented

### Database Models
- **Tour**: Complete tour packages with pricing, itinerary, and media
- **BookingInquiry**: Customer booking requests with status tracking
- **BlogPost**: Blog articles with SEO metadata and categories
- **Testimonial**: Customer reviews with approval workflow
- **User**: Admin users with role-based permissions
- **GalleryImage**: Photo gallery with categorization

### API Endpoints
- Complete REST API for tours, bookings, blog, testimonials
- Admin APIs for content management
- Authentication and authorization
- Email notifications and form handling

### SEO & Performance
- Structured data (JSON-LD) for search engines
- Automatic sitemap generation
- Image optimization and lazy loading
- Web Vitals monitoring
- Meta tags and Open Graph support

## 🚀 Deployment

The easiest way to deploy is using [Vercel](https://vercel.com):

1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

For other deployment options, see the [deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying).

## 📄 License

This project is licensed under the MIT License.
