import mongoose, { Document, Schema } from 'mongoose';

export interface IGalleryImage extends Document {
  title: string;
  description?: string;
  imageUrl: string;
  thumbnailUrl?: string;
  category: 'destinations' | 'activities' | 'testimonials' | 'tours' | 'culture' | 'wildlife';
  location?: string;
  tags: string[];
  tourId?: mongoose.Types.ObjectId;
  isActive: boolean;
  sortOrder: number;
  uploadedBy: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const GalleryImageSchema = new Schema<IGalleryImage>({
  title: { type: String, required: true },
  description: { type: String },
  imageUrl: { type: String, required: true },
  thumbnailUrl: { type: String },
  category: {
    type: String,
    enum: ['destinations', 'activities', 'testimonials', 'tours', 'culture', 'wildlife'],
    required: true
  },
  location: { type: String },
  tags: [{ type: String }],
  tourId: { type: Schema.Types.ObjectId, ref: 'Tour' },
  isActive: { type: Boolean, default: true },
  sortOrder: { type: Number, default: 0 },
  uploadedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
}, {
  timestamps: true
});

// Create indexes for better performance
GalleryImageSchema.index({ category: 1 });
GalleryImageSchema.index({ isActive: 1 });
GalleryImageSchema.index({ sortOrder: 1 });
GalleryImageSchema.index({ tags: 1 });
GalleryImageSchema.index({ tourId: 1 });

export default mongoose.models.GalleryImage || mongoose.model<IGalleryImage>('GalleryImage', GalleryImageSchema);
