import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('has correct title and meta description', async ({ page }) => {
    await expect(page).toHaveTitle(/Garrisons Tours/);
    
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', /Sri Lanka/);
  });

  test('displays hero section with main elements', async ({ page }) => {
    // Check hero heading
    const heroHeading = page.getByRole('heading', { level: 1 });
    await expect(heroHeading).toBeVisible();
    await expect(heroHeading).toContainText('Discover');

    // Check hero buttons
    const exploreButton = page.getByRole('link', { name: /explore tours/i });
    const planButton = page.getByRole('link', { name: /plan your journey/i });
    
    await expect(exploreButton).toBeVisible();
    await expect(planButton).toBeVisible();
  });

  test('navigation menu works correctly', async ({ page }) => {
    // Test desktop navigation
    const toursLink = page.getByRole('link', { name: /tours/i }).first();
    await expect(toursLink).toBeVisible();
    
    await toursLink.click();
    await expect(page).toHaveURL('/tours');
    
    // Go back to homepage
    await page.goto('/');
    
    // Test other navigation links
    const aboutLink = page.getByRole('link', { name: /about/i }).first();
    await aboutLink.click();
    await expect(page).toHaveURL('/about');
  });

  test('mobile navigation works', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Mobile menu should be visible
    const mobileMenuButton = page.getByRole('button', { name: /menu/i });
    await expect(mobileMenuButton).toBeVisible();
    
    // Click mobile menu
    await mobileMenuButton.click();
    
    // Check if mobile menu items are visible
    const mobileTours = page.getByRole('link', { name: /tours/i }).first();
    await expect(mobileTours).toBeVisible();
  });

  test('featured tours section displays correctly', async ({ page }) => {
    const featuredSection = page.getByRole('heading', { name: /featured tours/i });
    await expect(featuredSection).toBeVisible();
    
    // Check if tour cards are displayed
    const tourCards = page.locator('[data-testid="tour-card"]');
    await expect(tourCards.first()).toBeVisible();
  });

  test('testimonials section displays correctly', async ({ page }) => {
    const testimonialsSection = page.getByRole('heading', { name: /what our travelers say/i });
    await expect(testimonialsSection).toBeVisible();
    
    // Check if testimonial cards are displayed
    const testimonialCards = page.locator('[data-testid="testimonial-card"]');
    await expect(testimonialCards.first()).toBeVisible();
  });

  test('footer contains important links', async ({ page }) => {
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Check footer links
    const privacyLink = page.getByRole('link', { name: /privacy policy/i });
    const termsLink = page.getByRole('link', { name: /terms of service/i });
    const contactLink = page.getByRole('link', { name: /contact/i });
    
    await expect(privacyLink).toBeVisible();
    await expect(termsLink).toBeVisible();
    await expect(contactLink).toBeVisible();
  });

  test('contact information is displayed', async ({ page }) => {
    // Check if contact info is in header
    const phoneNumber = page.getByText('+94 77 123 4567');
    const email = page.getByText('<EMAIL>');
    
    await expect(phoneNumber).toBeVisible();
    await expect(email).toBeVisible();
  });

  test('page loads within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Page should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  test('images load correctly', async ({ page }) => {
    // Wait for images to load
    await page.waitForLoadState('networkidle');
    
    // Check if hero image is loaded
    const heroImage = page.locator('img').first();
    await expect(heroImage).toBeVisible();
    
    // Check if image has loaded (not broken)
    const naturalWidth = await heroImage.evaluate((img: HTMLImageElement) => img.naturalWidth);
    expect(naturalWidth).toBeGreaterThan(0);
  });

  test('responsive design works on different screen sizes', async ({ page }) => {
    // Test desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.getByRole('navigation')).toBeVisible();
    
    // Test tablet
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.getByRole('navigation')).toBeVisible();
    
    // Test mobile
    await page.setViewportSize({ width: 375, height: 667 });
    const mobileMenu = page.getByRole('button', { name: /menu/i });
    await expect(mobileMenu).toBeVisible();
  });

  test('accessibility features work correctly', async ({ page }) => {
    // Check if page has proper heading structure
    const h1 = page.getByRole('heading', { level: 1 });
    await expect(h1).toBeVisible();
    
    // Check if images have alt text
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      expect(alt).toBeTruthy();
    }
    
    // Check if links have accessible names
    const links = page.getByRole('link');
    const linkCount = await links.count();
    
    for (let i = 0; i < Math.min(linkCount, 10); i++) { // Check first 10 links
      const link = links.nth(i);
      const accessibleName = await link.textContent();
      expect(accessibleName?.trim()).toBeTruthy();
    }
  });

  test('search functionality works', async ({ page }) => {
    // If there's a search feature on homepage
    const searchInput = page.getByRole('textbox', { name: /search/i });
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('cultural');
      await searchInput.press('Enter');
      
      // Should navigate to search results or filter content
      await page.waitForLoadState('networkidle');
      expect(page.url()).toContain('search');
    }
  });
});
