import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Star, MapPin, Clock, Users } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center">
        <div className="absolute inset-0 z-0">
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: 'url("/Sri Lanka Sunset Landscape Garrisons Tours Logojpg-1489-X-551.jpg")'
            }}
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>
        
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-heading font-bold mb-6">
            Discover the
            <span className="text-amber-400"> Pearl of the Indian Ocean</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-gray-200">
            Experience authentic Sri Lankan culture, breathtaking landscapes, and unforgettable adventures with our personalized tour packages.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 text-lg font-semibold shadow-lg">
              <Link href="/tours">Explore Tours</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 text-lg font-semibold backdrop-blur-sm bg-white/10 transition-all duration-300">
              <Link href="/contact" className="text-white hover:text-gray-900 no-underline">
                Plan Your Journey
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Tours Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-heading font-bold text-gray-900 mb-4">
              Popular Tour Packages
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Carefully crafted experiences that showcase the best of Sri Lanka&apos;s culture, nature, and heritage.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Sample Tour Cards */}
            {[
              {
                title: "Cultural Triangle Explorer",
                duration: "7 Days",
                price: "$899",
                rating: 4.9,
                location: "Anuradhapura, Polonnaruwa, Sigiriya",
                description: "Discover ancient kingdoms and UNESCO World Heritage sites in Sri Lanka's Cultural Triangle."
              },
              {
                title: "Wildlife Safari Adventure",
                duration: "5 Days",
                price: "$699",
                rating: 4.8,
                location: "Yala, Udawalawe, Minneriya",
                description: "Experience Sri Lanka's incredible wildlife in national parks and nature reserves."
              },
              {
                title: "Hill Country & Tea Plantations",
                duration: "6 Days",
                price: "$799",
                rating: 4.9,
                location: "Kandy, Nuwara Eliya, Ella",
                description: "Journey through misty mountains, tea plantations, and charming hill stations."
              }
            ].map((tour, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-48 bg-gradient-to-br from-emerald-100 to-emerald-200 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-2">
                      <MapPin className="h-8 w-8 text-white" />
                    </div>
                    <p className="text-emerald-700 font-medium">{tour.location}</p>
                  </div>
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-sm font-semibold text-emerald-600">
                    {tour.price}
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2">{tour.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {tour.description}
                  </p>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-gray-500" />
                      <span className="text-sm text-gray-600">{tour.duration}</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                      <span className="text-sm font-medium">{tour.rating}</span>
                    </div>
                  </div>
                  <Button asChild className="w-full bg-emerald-600 hover:bg-emerald-700">
                    <Link href="/tours">View Details</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button asChild variant="outline" size="lg">
              <Link href="/tours">View All Tours</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-heading font-bold text-gray-900 mb-4">
              Why Choose Garrisons Tours?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We&apos;re not just tour operators - we&apos;re your local friends who know Sri Lanka inside out.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-emerald-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Local Expertise</h3>
              <p className="text-gray-600">
                Born and raised in Sri Lanka, we know the hidden gems and authentic experiences that guidebooks miss.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-amber-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-amber-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Personalized Service</h3>
              <p className="text-gray-600">
                Every tour is tailored to your interests, budget, and travel style for a truly unique experience.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-coral-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-coral-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Sustainable Tourism</h3>
              <p className="text-gray-600">
                We support local communities and practice responsible tourism to preserve Sri Lanka&apos;s natural beauty.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-heading font-bold mb-4">
            Ready to Start Your Sri Lankan Adventure?
          </h2>
          <p className="text-xl mb-8 text-emerald-100">
            Let us create a personalized itinerary that matches your dreams and budget.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-3 text-lg">
              <Link href="/contact">Get Free Quote</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white text-emerald-600 px-8 py-3 text-lg">
              <Link href="/tours">Browse Tours</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}