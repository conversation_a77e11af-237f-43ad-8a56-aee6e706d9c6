'use client';

import { useEffect } from 'react';
import Script from 'next/script';

// Google Analytics component
export function GoogleAnalytics({ measurementId }: { measurementId: string }) {
  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', measurementId, {
        page_title: document.title,
        page_location: window.location.href,
      });
    }
  }, [measurementId]);

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${measurementId}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  );
}

// Facebook Pixel component
export function FacebookPixel({ pixelId }: { pixelId: string }) {
  return (
    <Script id="facebook-pixel" strategy="afterInteractive">
      {`
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '${pixelId}');
        fbq('track', 'PageView');
      `}
    </Script>
  );
}

// Performance monitoring
export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Web Vitals monitoring
    interface WebVitalMetric {
      name: string;
      value: number;
      id: string;
    }
    
    const reportWebVitals = (metric: WebVitalMetric) => {
      if (window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          event_label: metric.id,
          non_interaction: true,
        });
      }
    };

    // Import web-vitals dynamically
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(reportWebVitals);
      getFID(reportWebVitals);
      getFCP(reportWebVitals);
      getLCP(reportWebVitals);
      getTTFB(reportWebVitals);
    });

    // Custom performance metrics
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navigationEntry = entry as PerformanceNavigationTiming;
          
          // Track page load time
          const pageLoadTime = navigationEntry.loadEventEnd - navigationEntry.fetchStart;
          if (window.gtag && pageLoadTime > 0) {
            window.gtag('event', 'page_load_time', {
              event_category: 'Performance',
              value: Math.round(pageLoadTime),
              non_interaction: true,
            });
          }
        }
      }
    });

    observer.observe({ entryTypes: ['navigation'] });

    return () => observer.disconnect();
  }, []);

  return null;
}

// Event tracking utilities
export const trackEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

export const trackBookingInquiry = (tourTitle: string, tourPrice: number) => {
  trackEvent('booking_inquiry', {
    event_category: 'Engagement',
    tour_title: tourTitle,
    tour_price: tourPrice,
    currency: 'USD',
  });
};

export const trackTestimonialSubmission = (rating: number) => {
  trackEvent('testimonial_submission', {
    event_category: 'Engagement',
    rating: rating,
  });
};

export const trackContactFormSubmission = () => {
  trackEvent('contact_form_submission', {
    event_category: 'Engagement',
  });
};

export const trackTourView = (tourTitle: string, tourCategory: string) => {
  trackEvent('tour_view', {
    event_category: 'Content',
    tour_title: tourTitle,
    tour_category: tourCategory,
  });
};

export const trackBlogPostView = (postTitle: string, postCategory: string) => {
  trackEvent('blog_post_view', {
    event_category: 'Content',
    post_title: postTitle,
    post_category: postCategory,
  });
};

// Declare global gtag function
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js',
      targetId: string | Date,
      config?: Record<string, unknown>
    ) => void;
    dataLayer: unknown[];
  }
}
