'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    // Log the critical error
    console.error('Critical application error:', error);
    
    // In production, send to error monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Critical error reporting
      // errorReportingService.captureException(error, { level: 'fatal' });
    }
  }, [error]);

  return (
    <html>
      <body>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
          <div className="max-w-2xl w-full text-center">
            <Card className="shadow-lg">
              <CardContent className="pt-12 pb-8">
                {/* Critical Error Icon */}
                <div className="mb-8">
                  <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <AlertTriangle className="h-12 w-12 text-red-600" />
                  </div>
                </div>

                {/* Error Message */}
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  Critical Error
                </h1>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                  We're experiencing a critical system error. Our technical team has been immediately notified and is working to resolve this issue.
                </p>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                  <Button 
                    onClick={reset} 
                    size="lg" 
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    <RefreshCw className="h-5 w-5 mr-2" />
                    Try Again
                  </Button>
                  
                  <Button 
                    onClick={() => window.location.href = '/'} 
                    variant="outline" 
                    size="lg"
                  >
                    <Home className="h-5 w-5 mr-2" />
                    Go to Homepage
                  </Button>
                </div>

                {/* Error Reference */}
                {error.digest && (
                  <div className="mt-6 p-4 bg-gray-100 rounded-lg">
                    <p className="text-sm text-gray-600 font-semibold mb-1">
                      Error Reference ID:
                    </p>
                    <p className="text-sm font-mono text-gray-800">
                      {error.digest}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      Please provide this ID when contacting support
                    </p>
                  </div>
                )}

                {/* Contact Information */}
                <div className="mt-8 p-4 bg-emerald-50 rounded-lg">
                  <p className="text-sm text-emerald-800 font-medium mb-2">
                    Need immediate assistance?
                  </p>
                  <div className="text-sm text-emerald-700">
                    <p>📧 Email: <EMAIL></p>
                    <p>📞 Phone: +94 77 123 4567</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status Information */}
            <div className="mt-8 text-center">
              <p className="text-gray-500 text-sm">
                Check our{' '}
                <a 
                  href="https://status.garrisonstours.com" 
                  className="text-emerald-600 hover:text-emerald-700 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  status page
                </a>{' '}
                for real-time updates on system availability.
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
