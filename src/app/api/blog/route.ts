import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BlogPost from '@/models/BlogPost';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const published = searchParams.get('published') !== 'false'; // Default to published only
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, any> = {};
    if (published) {
      query.isPublished = true;
    }
    if (category && category !== 'all') {
      query.category = { $in: [category] };
    }

    // Get blog posts with pagination
    const posts = await BlogPost.find(query)
      .sort({ publishedAt: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('relatedTours', 'title slug')
      .lean();

    // Get total count for pagination
    const total = await BlogPost.countDocuments(query);

    return NextResponse.json({
      posts,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: skip + posts.length < total
    });

  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    
    // Create new blog post
    const blogPost = new BlogPost({
      ...body,
      publishedAt: body.isPublished ? new Date() : undefined
    });
    
    await blogPost.save();

    return NextResponse.json(blogPost, { status: 201 });

  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}
