# Garrisons Tours - Deployment Checklist

## Pre-Deployment Checklist

### ✅ Code Quality & Testing
- [ ] All TypeScript errors resolved
- [ ] ESLint warnings addressed
- [ ] Build process completes successfully (`npm run build`)
- [ ] All API endpoints tested
- [ ] Authentication flow verified
- [ ] Admin panel functionality tested
- [ ] Public website features tested
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility checked

### ✅ Environment Configuration
- [ ] Production environment variables configured
- [ ] Database connection string updated
- [ ] SMTP email settings configured
- [ ] NextAuth secret generated (secure random string)
- [ ] Admin credentials set
- [ ] Analytics tracking ID added (if using)
- [ ] Domain-specific URLs updated

### ✅ Database Setup
- [ ] Production MongoDB database created
- [ ] Database user with appropriate permissions
- [ ] Network access configured (IP whitelist)
- [ ] Backup strategy implemented
- [ ] Indexes created for performance
- [ ] Initial admin user created

### ✅ Security Configuration
- [ ] Strong passwords for all accounts
- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] CORS settings reviewed
- [ ] Rate limiting implemented
- [ ] Input validation in place
- [ ] Error messages don't expose sensitive data

### ✅ Performance Optimization
- [ ] Images optimized and compressed
- [ ] Caching headers configured
- [ ] Database queries optimized
- [ ] Bundle size analyzed
- [ ] Core Web Vitals tested
- [ ] CDN configured (if applicable)

## Deployment Steps

### Option 1: Vercel Deployment (Recommended)

#### Step 1: Prepare Repository
```bash
# Ensure code is committed and pushed
git add .
git commit -m "Prepare for production deployment"
git push origin main
```

#### Step 2: Vercel Setup
1. **Create Vercel Account**
   - Sign up at [vercel.com](https://vercel.com)
   - Connect your GitHub account

2. **Import Project**
   - Click "New Project"
   - Import your GitHub repository
   - Select "Next.js" framework preset

3. **Configure Environment Variables**
   ```
   MONGODB_URI=mongodb+srv://user:<EMAIL>/garrisons-tours
   NEXTAUTH_SECRET=your-super-secure-production-secret-here
   NEXTAUTH_URL=https://your-domain.vercel.app
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=secure-admin-password
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
   ```

4. **Deploy**
   - Click "Deploy"
   - Wait for build to complete
   - Test deployment URL

#### Step 3: Custom Domain (Optional)
1. **Add Domain**
   - Go to Project Settings → Domains
   - Add your custom domain
   - Configure DNS records as instructed

2. **SSL Certificate**
   - Automatically provided by Vercel
   - Verify HTTPS is working

### Option 2: Self-Hosted Deployment

#### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

#### Step 2: Application Deployment
```bash
# Clone repository
git clone <your-repo-url>
cd garrisons-tours

# Install dependencies
npm install

# Create production environment file
cp .env.example .env.local
# Edit .env.local with production values

# Build application
npm run build

# Start with PM2
pm2 start npm --name "garrisons-tours" -- start
pm2 save
pm2 startup
```

#### Step 3: Nginx Configuration
```nginx
# /etc/nginx/sites-available/garrisons-tours
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/garrisons-tours /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### Step 4: SSL Certificate
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

## Post-Deployment Verification

### ✅ Functionality Testing
- [ ] Homepage loads correctly
- [ ] Tour listings display properly
- [ ] Individual tour pages work
- [ ] Booking form submits successfully
- [ ] Contact form sends emails
- [ ] Blog posts display correctly
- [ ] Gallery images load properly
- [ ] Testimonials page works
- [ ] Admin login functions
- [ ] Admin dashboard loads
- [ ] Tour management works
- [ ] Booking management functions
- [ ] All API endpoints respond correctly

### ✅ Performance Testing
- [ ] Page load times under 3 seconds
- [ ] Images load quickly
- [ ] Mobile performance acceptable
- [ ] Core Web Vitals pass
- [ ] No console errors
- [ ] Database queries perform well

### ✅ SEO Verification
- [ ] Meta tags display correctly
- [ ] Structured data validates
- [ ] Sitemap accessible (/sitemap.xml)
- [ ] Robots.txt accessible (/robots.txt)
- [ ] Google Search Console configured
- [ ] Analytics tracking works

### ✅ Security Testing
- [ ] HTTPS enforced
- [ ] Admin panel requires authentication
- [ ] Unauthorized access blocked
- [ ] Form submissions validated
- [ ] No sensitive data exposed
- [ ] Security headers present

## Monitoring Setup

### Application Monitoring
```bash
# Set up log monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### Database Monitoring
- [ ] MongoDB Atlas monitoring enabled
- [ ] Backup schedule configured
- [ ] Performance alerts set up
- [ ] Connection monitoring active

### Uptime Monitoring
- [ ] Uptime monitoring service configured
- [ ] Alert notifications set up
- [ ] Status page created (optional)

## Maintenance Schedule

### Daily Tasks
- [ ] Check application logs
- [ ] Monitor error rates
- [ ] Review new bookings
- [ ] Check email delivery

### Weekly Tasks
- [ ] Review performance metrics
- [ ] Check backup integrity
- [ ] Update content as needed
- [ ] Review security logs

### Monthly Tasks
- [ ] Update dependencies
- [ ] Review and approve testimonials
- [ ] Analyze traffic and conversions
- [ ] Update tour information
- [ ] Security audit

## Rollback Plan

### If Deployment Fails
1. **Identify Issue**
   - Check build logs
   - Review error messages
   - Test locally

2. **Quick Rollback**
   ```bash
   # Vercel: Rollback to previous deployment
   # Self-hosted: Revert to previous version
   git checkout previous-working-commit
   npm run build
   pm2 restart garrisons-tours
   ```

3. **Fix and Redeploy**
   - Address the issue
   - Test thoroughly
   - Deploy again

### Emergency Contacts
- [ ] Development team contact info
- [ ] Hosting provider support
- [ ] Domain registrar support
- [ ] Email service provider support

## Go-Live Checklist

### Final Steps Before Launch
- [ ] All testing completed
- [ ] Content reviewed and approved
- [ ] Legal pages updated (Privacy, Terms)
- [ ] Contact information verified
- [ ] Social media links updated
- [ ] Google Analytics configured
- [ ] Search Console submitted
- [ ] Team trained on admin panel

### Launch Day
- [ ] Monitor application closely
- [ ] Check all critical paths
- [ ] Verify email notifications
- [ ] Test booking process
- [ ] Monitor performance metrics
- [ ] Be ready for quick fixes

### Post-Launch (First Week)
- [ ] Daily monitoring
- [ ] User feedback collection
- [ ] Performance optimization
- [ ] Content updates as needed
- [ ] SEO monitoring
- [ ] Backup verification

---

## Support Information

### Technical Support
- **Documentation**: USER_GUIDE.md, TECHNICAL_GUIDE.md
- **Repository**: GitHub repository with issues
- **Development Team**: Contact information

### Service Providers
- **Hosting**: Vercel or your hosting provider
- **Database**: MongoDB Atlas support
- **Email**: Your SMTP provider support
- **Domain**: Your domain registrar

---

**Congratulations!** 🎉 Your Garrisons Tours website is now live and ready to help customers discover the beauty of Sri Lanka!
