import { NextResponse } from 'next/server';
import * as bcrypt from 'bcrypt';
import connectDB from '@/lib/mongodb';
import Tour from '@/models/Tour';
import BlogPost from '@/models/BlogPost';
import Testimonial from '@/models/Testimonial';
import User from '@/models/User';
import GalleryImage from '@/models/Gallery';

const sampleTours = [
  {
    title: 'Cultural Triangle Explorer',
    slug: 'cultural-triangle-explorer',
    description: 'Embark on a fascinating journey through Sri Lanka\'s Cultural Triangle, home to ancient kingdoms and UNESCO World Heritage sites. This comprehensive tour takes you through the historic cities of Anuradhapura, Polonnaruwa, and the iconic Sigiriya Rock Fortress. Discover centuries-old temples, intricate stone carvings, and the rich Buddhist heritage that defines Sri Lankan culture.',
    shortDescription: 'Discover ancient kingdoms and UNESCO World Heritage sites in Sri Lanka\'s Cultural Triangle.',
    itinerary: [
      {
        day: 1,
        title: 'Arrival in Colombo & Transfer to Anuradhapura',
        description: 'Welcome to Sri Lanka! Transfer to the ancient city of Anuradhapura.',
        activities: ['Airport pickup', 'City orientation', 'Hotel check-in'],
        accommodation: 'Heritage Hotel Anuradhapura',
        meals: ['dinner']
      },
      {
        day: 2,
        title: 'Anuradhapura Ancient City Tour',
        description: 'Explore the sacred city of Anuradhapura with its ancient stupas and monasteries.',
        activities: ['Sri Maha Bodhi Tree visit', 'Ruwanwelisaya Stupa', 'Jetavanaramaya'],
        accommodation: 'Heritage Hotel Anuradhapura',
        meals: ['breakfast', 'lunch', 'dinner']
      }
    ],
    price: {
      amount: 899,
      currency: 'USD',
      priceType: 'per_person'
    },
    duration: {
      days: 7,
      nights: 6
    },
    images: {
      featured: '/cultural-triangle.jpg',
      gallery: ['/anuradhapura.jpg', '/polonnaruwa.jpg', '/sigiriya.jpg'],
      alt: ['Cultural Triangle', 'Ancient Anuradhapura', 'Polonnaruwa Ruins', 'Sigiriya Rock']
    },
    category: 'cultural',
    highlights: [
      'UNESCO World Heritage Sites',
      'Ancient Buddhist temples',
      'Sigiriya Rock Fortress climb',
      'Traditional village experience',
      'Local cuisine tasting'
    ],
    included: [
      'Accommodation (6 nights)',
      'All meals as specified',
      'Professional English-speaking guide',
      'Transportation in air-conditioned vehicle',
      'Entrance fees to all sites'
    ],
    excluded: [
      'International flights',
      'Visa fees',
      'Personal expenses',
      'Tips and gratuities',
      'Travel insurance'
    ],
    difficulty: 'moderate',
    maxGroupSize: 12,
    isActive: true,
    seoMeta: {
      title: 'Cultural Triangle Explorer - 7 Day Sri Lanka Heritage Tour',
      description: 'Explore Sri Lanka\'s ancient kingdoms on this 7-day Cultural Triangle tour. Visit Anuradhapura, Polonnaruwa, and Sigiriya with expert guides.',
      keywords: ['Sri Lanka cultural tour', 'Cultural Triangle', 'Sigiriya', 'Anuradhapura', 'Polonnaruwa', 'UNESCO sites']
    }
  },
  {
    title: 'Wildlife Safari Adventure',
    slug: 'wildlife-safari-adventure',
    description: 'Experience the incredible biodiversity of Sri Lanka on this thrilling wildlife safari adventure. Visit three of the island\'s premier national parks: Yala, Udawalawe, and Minneriya. Encounter majestic elephants, elusive leopards, and hundreds of bird species in their natural habitats.',
    shortDescription: 'Experience Sri Lanka\'s incredible wildlife in Yala, Udawalawe, and Minneriya National Parks.',
    itinerary: [
      {
        day: 1,
        title: 'Arrival & Transfer to Yala',
        description: 'Arrive and transfer to Yala National Park area.',
        activities: ['Airport pickup', 'Transfer to Yala', 'Evening at leisure'],
        accommodation: 'Yala Safari Lodge',
        meals: ['dinner']
      },
      {
        day: 2,
        title: 'Yala National Park Safari',
        description: 'Full day safari in Yala National Park, famous for its leopard population.',
        activities: ['Morning safari', 'Afternoon safari', 'Wildlife photography'],
        accommodation: 'Yala Safari Lodge',
        meals: ['breakfast', 'lunch', 'dinner']
      }
    ],
    price: {
      amount: 699,
      currency: 'USD',
      priceType: 'per_person'
    },
    duration: {
      days: 5,
      nights: 4
    },
    images: {
      featured: '/wildlife-safari.jpg',
      gallery: ['/yala-leopard.jpg', '/elephant-herd.jpg', '/bird-watching.jpg'],
      alt: ['Wildlife Safari', 'Yala Leopard', 'Elephant Herd', 'Bird Watching']
    },
    category: 'wildlife',
    highlights: [
      'Yala National Park safari',
      'Udawalawe elephant sanctuary',
      'Minneriya elephant gathering',
      'Professional wildlife guide',
      'Photography opportunities'
    ],
    included: [
      'Accommodation (4 nights)',
      'All meals',
      'Safari jeep with driver',
      'Professional naturalist guide',
      'Park entrance fees'
    ],
    excluded: [
      'International flights',
      'Visa fees',
      'Personal expenses',
      'Alcoholic beverages',
      'Travel insurance'
    ],
    difficulty: 'easy',
    maxGroupSize: 8,
    isActive: true,
    seoMeta: {
      title: 'Wildlife Safari Adventure - 5 Day Sri Lanka Safari Tour',
      description: 'Join our 5-day wildlife safari to see leopards, elephants, and exotic birds in Yala, Udawalawe, and Minneriya National Parks.',
      keywords: ['Sri Lanka safari', 'Yala National Park', 'wildlife tour', 'leopard safari', 'elephant watching']
    }
  },
  {
    title: 'Hill Country & Tea Plantations',
    slug: 'hill-country-tea-plantations',
    description: 'Journey through the misty mountains and emerald tea plantations of Sri Lanka\'s hill country. Experience the colonial charm of Kandy, the cool climate of Nuwara Eliya, and the breathtaking views from Ella. Learn about tea production and enjoy scenic train rides through some of the world\'s most beautiful landscapes.',
    shortDescription: 'Journey through misty mountains, tea plantations, and charming hill stations.',
    itinerary: [
      {
        day: 1,
        title: 'Colombo to Kandy',
        description: 'Travel to the cultural capital of Kandy.',
        activities: ['Temple of the Tooth visit', 'Kandy Lake walk', 'Cultural show'],
        accommodation: 'Kandy Heritage Hotel',
        meals: ['lunch', 'dinner']
      }
    ],
    price: {
      amount: 799,
      currency: 'USD',
      priceType: 'per_person'
    },
    duration: {
      days: 6,
      nights: 5
    },
    images: {
      featured: '/tea-plantations.jpg',
      gallery: ['/kandy-temple.jpg', '/nuwara-eliya.jpg', '/ella-view.jpg'],
      alt: ['Tea Plantations', 'Kandy Temple', 'Nuwara Eliya', 'Ella View']
    },
    category: 'adventure',
    highlights: [
      'Temple of the Tooth Relic',
      'Tea factory visits',
      'Scenic train journey',
      'Nine Arch Bridge',
      'Little Adam\'s Peak hike'
    ],
    included: [
      'Accommodation (5 nights)',
      'Daily breakfast',
      'Transportation',
      'Train tickets',
      'Entrance fees'
    ],
    excluded: [
      'Lunch and dinner (except specified)',
      'International flights',
      'Personal expenses',
      'Tips',
      'Travel insurance'
    ],
    difficulty: 'moderate',
    maxGroupSize: 10,
    isActive: true,
    seoMeta: {
      title: 'Hill Country & Tea Plantations - 6 Day Sri Lanka Mountain Tour',
      description: 'Explore Sri Lanka\'s beautiful hill country, tea plantations, and mountain towns on this 6-day scenic tour.',
      keywords: ['Sri Lanka hill country', 'tea plantation tour', 'Kandy', 'Nuwara Eliya', 'Ella', 'train journey']
    }
  }
];

export async function POST() {
  try {
    await connectDB();

    // Clear existing data
    await Tour.deleteMany({});
    await BlogPost.deleteMany({});
    await Testimonial.deleteMany({});
    await GalleryImage.deleteMany({});

    // Create admin user if it doesn't exist
    const existingAdmin = await User.findOne({ role: 'admin' });
    let adminUser;

    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash('admin123', 12);
      adminUser = await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        isActive: true
      });
    } else {
      adminUser = existingAdmin;
    }

    // Insert sample tours
    const tours = await Tour.insertMany(sampleTours);

    // Insert sample blog posts
    const sampleBlogPosts = [
      {
        title: 'Top 10 Must-Visit Places in Sri Lanka',
        slug: 'top-10-must-visit-places-sri-lanka',
        excerpt: 'Discover the most breathtaking destinations that make Sri Lanka the Pearl of the Indian Ocean.',
        content: 'Sri Lanka, known as the Pearl of the Indian Ocean, offers an incredible diversity of experiences...',
        featuredImage: '/blog/top-10-places.jpg',
        author: {
          name: 'Garrisons Tours Team',
          avatar: '/team/author.jpg'
        },
        category: ['travel-tips', 'destinations'],
        tags: ['sri lanka', 'travel', 'destinations', 'culture'],
        relatedTours: [tours[0]._id, tours[1]._id],
        isPublished: true,
        publishedAt: new Date(),
        seoMeta: {
          title: 'Top 10 Must-Visit Places in Sri Lanka - Travel Guide',
          description: 'Discover the most breathtaking destinations in Sri Lanka with our comprehensive travel guide.',
          keywords: ['sri lanka destinations', 'travel guide', 'places to visit']
        }
      }
    ];

    const blogPosts = await BlogPost.insertMany(sampleBlogPosts);

    // Insert sample testimonials
    const sampleTestimonials = [
      {
        customerName: 'John Smith',
        customerEmail: '<EMAIL>',
        customerCountry: 'United Kingdom',
        tourId: tours[0]._id,
        tourTitle: tours[0].title,
        rating: 5,
        title: 'Amazing Cultural Experience',
        content: 'Our trip to Sri Lanka with Garrisons Tours was absolutely incredible. The cultural sites were breathtaking and our guide was extremely knowledgeable.',
        travelDate: new Date('2024-01-15'),
        isApproved: true,
        isFeatured: true,
        submittedAt: new Date('2024-01-25'),
        approvedAt: new Date('2024-01-26')
      }
    ];

    const testimonials = await Testimonial.insertMany(sampleTestimonials);

    return NextResponse.json({
      message: 'Database seeded successfully',
      data: {
        tours: tours.length,
        blogPosts: blogPosts.length,
        testimonials: testimonials.length,
        adminUser: adminUser.email
      }
    });

  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      { error: 'Failed to seed database' },
      { status: 500 }
    );
  }
}