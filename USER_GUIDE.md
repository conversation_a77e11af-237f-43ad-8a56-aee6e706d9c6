# Garrisons Tours - Complete User Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Installation & Setup](#installation--setup)
3. [Admin Panel Guide](#admin-panel-guide)
4. [Content Management](#content-management)
5. [Customer Features](#customer-features)
6. [API Documentation](#api-documentation)
7. [Deployment Guide](#deployment-guide)
8. [Troubleshooting](#troubleshooting)

## Quick Start

### Prerequisites
- Node.js 18+ installed
- MongoDB database (local or cloud)
- SMTP email service (optional but recommended)

### 5-Minute Setup
```bash
# 1. Clone and install
git clone <repository-url>
cd garrisons-tours
npm install

# 2. Configure environment
cp .env.example .env.local
# Edit .env.local with your settings

# 3. Start development server
npm run dev

# 4. Access the application
# Public site: http://localhost:3000
# Admin panel: http://localhost:3000/admin
```

## Installation & Setup

### Environment Variables
Create `.env.local` file with these required variables:

```env
# Database Connection
MONGODB_URI=mongodb://localhost:27017/garrisons-tours
# Or for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/garrisons-tours

# Authentication
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Default Admin Account
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Analytics (Optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### Database Setup

#### Option 1: Local MongoDB
```bash
# Install MongoDB locally
# macOS
brew install mongodb-community

# Start MongoDB
brew services start mongodb-community

# Database will be created automatically
```

#### Option 2: MongoDB Atlas (Cloud)
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get connection string and add to `MONGODB_URI`
4. Whitelist your IP address

### Initial Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Create admin user (run once)
curl -X POST http://localhost:3000/api/admin/setup

# Optional: Seed sample data
curl -X POST http://localhost:3000/api/seed
```

## Admin Panel Guide

### Accessing Admin Panel
1. Navigate to `http://localhost:3000/admin`
2. Login with credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`

### Dashboard Overview
The admin dashboard provides:
- **Key Metrics**: Tours, bookings, testimonials, revenue
- **Recent Activity**: Latest bookings and testimonials
- **Quick Actions**: Create tours, manage bookings
- **Pending Items**: Items requiring attention

### Navigation Menu
- **Dashboard**: Overview and analytics
- **Tours**: Manage tour packages
- **Bookings**: Handle customer inquiries
- **Testimonials**: Review and approve testimonials
- **Blog Posts**: Manage travel blog content
- **Gallery**: Upload and organize photos
- **Users**: Manage admin accounts
- **Settings**: System configuration

## Content Management

### Managing Tours

#### Creating a New Tour
1. Go to **Admin → Tours → Add New Tour**
2. Fill in basic information:
   - **Title**: Tour name (e.g., "Cultural Triangle Explorer")
   - **Slug**: URL-friendly version (auto-generated)
   - **Short Description**: Brief summary for listings
   - **Full Description**: Detailed tour description
   - **Category**: Cultural, Wildlife, Adventure, etc.
   - **Difficulty**: Easy, Moderate, Challenging
   - **Duration**: Days and nights
   - **Price**: Amount in USD
   - **Group Size**: Maximum participants

3. Add tour details:
   - **Highlights**: Key attractions and experiences
   - **Included**: What's covered in the price
   - **Excluded**: What customers need to pay extra for

4. Create itinerary:
   - Add day-by-day breakdown
   - Include activities, accommodation, meals
   - Provide detailed descriptions

5. SEO settings:
   - **Meta Title**: Search engine title
   - **Meta Description**: Search snippet
   - **Keywords**: Relevant search terms

6. Set status and save

#### Managing Existing Tours
- **View All Tours**: See complete list with filters
- **Edit Tour**: Update any information
- **Activate/Deactivate**: Control visibility
- **Delete**: Remove tours (with confirmation)

### Managing Bookings

#### Booking Workflow
1. **New**: Fresh inquiry from customer
2. **Contacted**: You've reached out to customer
3. **Quoted**: Price quote sent
4. **Confirmed**: Booking confirmed and paid
5. **Cancelled**: Booking cancelled

#### Processing Bookings
1. Go to **Admin → Bookings**
2. View booking details:
   - Customer information
   - Travel dates and group size
   - Special requests
   - Contact details

3. Update status as you progress
4. Add internal notes for team reference
5. Use filters to find specific bookings

### Managing Blog Posts

#### Creating Blog Posts
1. Go to **Admin → Blog → New Post**
2. Fill in content:
   - **Title**: Engaging headline
   - **Slug**: URL-friendly version
   - **Excerpt**: Summary for listings
   - **Content**: Full article content
   - **Category**: Travel tips, destinations, etc.
   - **Tags**: Relevant keywords

3. SEO optimization:
   - Meta title and description
   - Featured image
   - Publication date

4. Publish or save as draft

### Managing Testimonials

#### Testimonial Approval Process
1. Customers submit testimonials via public form
2. Reviews appear in **Admin → Testimonials** as "Pending"
3. Review content for appropriateness
4. Approve or reject testimonials
5. Featured testimonials appear on homepage

### Managing Gallery

#### Uploading Photos
1. Go to **Admin → Gallery**
2. Click **Upload Photos**
3. Select multiple images
4. Add details for each:
   - Title and description
   - Category (destinations, activities, etc.)
   - Location information
   - Associated tour (if applicable)

5. Set active status for public visibility

## Customer Features

### Public Website Features

#### Tour Browsing
- **Tour Listings**: Grid view with filtering
- **Search**: Find tours by keyword
- **Categories**: Filter by tour type
- **Tour Details**: Complete information pages
- **Related Tours**: Suggestions based on category

#### Booking Process
1. **Select Tour**: Choose from tour listings
2. **Booking Form**: Multi-step process
   - Personal information
   - Travel dates and group size
   - Special requests
3. **Confirmation**: Booking reference provided
4. **Email Notifications**: Automatic confirmations

#### Blog & Content
- **Travel Blog**: Articles and guides
- **Photo Gallery**: Categorized images with lightbox
- **Testimonials**: Customer reviews and ratings
- **About & Contact**: Company information

### Customer Journey
1. **Discovery**: Browse tours and read blog
2. **Research**: View detailed tour information
3. **Inquiry**: Submit booking form
4. **Communication**: Receive quotes and updates
5. **Booking**: Confirm and pay for tour
6. **Experience**: Enjoy the tour
7. **Review**: Submit testimonial

## API Documentation

### Public Endpoints

#### Tours
```
GET /api/tours
- Query params: page, limit, category, search
- Returns: Paginated tour listings

GET /api/tours/[slug]
- Returns: Detailed tour information
```

#### Bookings
```
POST /api/bookings
- Body: Customer and travel details
- Returns: Booking confirmation
```

#### Blog
```
GET /api/blog
- Query params: page, limit, category
- Returns: Published blog posts

GET /api/blog/[slug]
- Returns: Individual blog post
```

#### Testimonials
```
GET /api/testimonials
- Query params: page, limit, approved=true
- Returns: Approved testimonials

POST /api/testimonials
- Body: Customer review data
- Returns: Submission confirmation
```

### Admin Endpoints

#### Authentication Required
All admin endpoints require authentication via NextAuth.js session.

#### Tours Management
```
POST /api/tours
- Body: Complete tour data
- Returns: Created tour

PUT /api/tours/[slug]
- Body: Updated tour data
- Returns: Updated tour

DELETE /api/tours/[slug]
- Returns: Deletion confirmation
```

#### Bookings Management
```
GET /api/admin/bookings
- Query params: page, limit, status
- Returns: All booking inquiries

PUT /api/admin/bookings/[id]
- Body: Status update and notes
- Returns: Updated booking
```

## Deployment Guide

### Production Environment Setup

#### Environment Variables for Production
```env
# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/garrisons-tours

# Authentication
NEXTAUTH_SECRET=super-secure-production-secret
NEXTAUTH_URL=https://yourdomain.com

# Email
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=secure-email-password

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### Deployment Options

#### Option 1: Vercel (Recommended)
1. **Connect Repository**:
   - Link GitHub repository to Vercel
   - Configure environment variables in dashboard
   - Enable automatic deployments

2. **Domain Setup**:
   - Add custom domain in Vercel settings
   - Configure DNS records
   - SSL certificate automatically provided

#### Option 2: Self-Hosted
1. **Server Requirements**:
   - Node.js 18+
   - PM2 for process management
   - Nginx for reverse proxy
   - SSL certificate (Let's Encrypt)

2. **Deployment Steps**:
```bash
# Build application
npm run build

# Start with PM2
pm2 start npm --name "garrisons-tours" -- start

# Configure Nginx
# Add SSL certificate
```

### Database Considerations
- **Backup Strategy**: Regular automated backups
- **Indexing**: Ensure proper database indexes
- **Monitoring**: Set up database monitoring
- **Scaling**: Consider read replicas for high traffic

## Troubleshooting

### Common Issues

#### Database Connection Issues
```
Error: MongooseError: Operation `tours.find()` buffering timed out
```
**Solution**: Check MongoDB connection string and network access

#### Authentication Problems
```
Error: [next-auth][error][SIGNIN_EMAIL_ERROR]
```
**Solution**: Verify SMTP configuration and email credentials

#### Build Errors
```
Error: Module not found
```
**Solution**: Run `npm install` and check for missing dependencies

#### Performance Issues
- **Slow Loading**: Optimize images and enable caching
- **High Memory Usage**: Check for memory leaks in API routes
- **Database Queries**: Add proper indexes and optimize queries

### Getting Help

#### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check application logs
tail -f logs/application.log
```

#### Support Resources
- **Documentation**: Check this guide and code comments
- **GitHub Issues**: Report bugs and feature requests
- **Community**: Join discussions and get help
- **Professional Support**: Contact development team

### Maintenance Tasks

#### Regular Maintenance
- **Database Cleanup**: Remove old sessions and logs
- **Image Optimization**: Compress and optimize uploaded images
- **Security Updates**: Keep dependencies updated
- **Backup Verification**: Test backup restoration process
- **Performance Monitoring**: Review analytics and performance metrics

#### Monthly Tasks
- Review and approve pending testimonials
- Update tour information and pricing
- Check for broken links and images
- Review booking conversion rates
- Update blog content and SEO

---

## Quick Reference

### Default Credentials
- **Admin Email**: <EMAIL>
- **Admin Password**: admin123

### Important URLs
- **Public Site**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin
- **API Docs**: http://localhost:3000/api

### Support
- **Email**: <EMAIL>
- **Documentation**: This guide
- **GitHub**: Repository issues section

---

*This guide covers the essential features and operations of the Garrisons Tours website. For advanced customization or specific requirements, refer to the code documentation or contact the development team.*
