import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import connectDB from '@/lib/mongodb';
import BookingInquiry from '@/models/BookingInquiry';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user?.role || !['admin', 'editor'].includes(session.user.role)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = params;
    const body = await request.json();

    // Update booking inquiry
    const updatedBooking = await BookingInquiry.findByIdAndUpdate(
      id,
      {
        ...body,
        lastContactedAt: body.status !== 'new' ? new Date() : undefined
      },
      { new: true, runValidators: true }
    ).populate('tourId', 'title slug price');

    if (!updatedBooking) {
      return NextResponse.json(
        { error: 'Booking inquiry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedBooking);

  } catch (error) {
    console.error('Error updating booking inquiry:', error);
    return NextResponse.json(
      { error: 'Failed to update booking inquiry' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session || !session.user?.role || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const { id } = params;

    // Delete booking inquiry
    const deletedBooking = await BookingInquiry.findByIdAndDelete(id);

    if (!deletedBooking) {
      return NextResponse.json(
        { error: 'Booking inquiry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Booking inquiry deleted successfully' });

  } catch (error) {
    console.error('Error deleting booking inquiry:', error);
    return NextResponse.json(
      { error: 'Failed to delete booking inquiry' },
      { status: 500 }
    );
  }
}
