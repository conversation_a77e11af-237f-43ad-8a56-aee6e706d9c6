import connectDB from '@/lib/mongodb';
import Tour from '@/models/Tour';
import BlogPost from '@/models/BlogPost';
import Testimonial from '@/models/Testimonial';

interface MigrationOptions {
  dryRun?: boolean;
  verbose?: boolean;
  batchSize?: number;
}

class ContentMigrator {
  private options: MigrationOptions;

  constructor(options: MigrationOptions = {}) {
    this.options = {
      dryRun: false,
      verbose: false,
      batchSize: 100,
      ...options
    };
  }

  private log(message: string, level: 'info' | 'warn' | 'error' = 'info') {
    if (this.options.verbose || level !== 'info') {
      const prefix = this.options.dryRun ? '[DRY RUN] ' : '';
      const timestamp = new Date().toISOString();
      console.log(`${timestamp} ${prefix}[${level.toUpperCase()}] ${message}`);
    }
  }

  // Migration: Add SEO fields to existing tours
  async addSEOFieldsToTours() {
    this.log('Starting SEO fields migration for tours...');
    
    try {
      const tours = await Tour.find({ 'seoMeta.title': { $exists: false } });
      this.log(`Found ${tours.length} tours without SEO fields`);

      let updated = 0;
      for (const tour of tours) {
        const seoMeta = {
          title: `${tour.title} - Sri Lanka Tour Package`,
          description: tour.shortDescription || tour.description?.substring(0, 160) || '',
          keywords: [
            'Sri Lanka tour',
            tour.category,
            tour.title.toLowerCase(),
            ...tour.highlights?.slice(0, 3) || [],
            `${tour.duration.days} day tour`
          ]
        };

        if (!this.options.dryRun) {
          await Tour.findByIdAndUpdate(tour._id, { seoMeta });
        }
        
        updated++;
        this.log(`Updated SEO for tour: ${tour.title}`);
      }

      this.log(`SEO migration completed. Updated ${updated} tours.`);
      return { success: true, updated };

    } catch (error) {
      this.log(`SEO migration failed: ${error}`, 'error');
      throw error;
    }
  }

  // Migration: Convert old image paths to new format
  async migrateImagePaths() {
    this.log('Starting image path migration...');
    
    try {
      const tours = await Tour.find({});
      let updated = 0;

      for (const tour of tours) {
        let needsUpdate = false;
        const updates: Record<string, unknown> = {};

        // Update featured image path
        if (tour.images?.featured && !tour.images.featured.startsWith('/images/')) {
          updates['images.featured'] = `/images/tours/${tour.images.featured}`;
          needsUpdate = true;
        }

        // Update gallery image paths
        if (tour.images?.gallery && Array.isArray(tour.images.gallery)) {
          const updatedGallery = tour.images.gallery.map(img => 
            img.startsWith('/images/') ? img : `/images/tours/${img}`
          );
          
          if (JSON.stringify(updatedGallery) !== JSON.stringify(tour.images.gallery)) {
            updates['images.gallery'] = updatedGallery;
            needsUpdate = true;
          }
        }

        if (needsUpdate && !this.options.dryRun) {
          await Tour.findByIdAndUpdate(tour._id, updates);
          updated++;
          this.log(`Updated image paths for tour: ${tour.title}`);
        }
      }

      // Update blog post images
      const blogPosts = await BlogPost.find({});
      for (const post of blogPosts) {
        if (post.featuredImage && !post.featuredImage.startsWith('/images/')) {
          if (!this.options.dryRun) {
            await BlogPost.findByIdAndUpdate(post._id, {
              featuredImage: `/images/blog/${post.featuredImage}`
            });
          }
          updated++;
          this.log(`Updated image path for blog post: ${post.title}`);
        }
      }

      this.log(`Image path migration completed. Updated ${updated} items.`);
      return { success: true, updated };

    } catch (error) {
      this.log(`Image path migration failed: ${error}`, 'error');
      throw error;
    }
  }

  // Migration: Add missing fields to testimonials
  async addMissingTestimonialFields() {
    this.log('Starting testimonial fields migration...');
    
    try {
      const testimonials = await Testimonial.find({});
      let updated = 0;

      for (const testimonial of testimonials) {
        const updates: Record<string, unknown> = {};
        let needsUpdate = false;

        // Add default approval status if missing
        if (testimonial.isApproved === undefined) {
          updates.isApproved = true; // Assume existing testimonials are approved
          needsUpdate = true;
        }

        // Add featured status if missing
        if (testimonial.isFeatured === undefined) {
          updates.isFeatured = testimonial.rating >= 5; // Feature 5-star reviews
          needsUpdate = true;
        }

        // Add submission date if missing
        if (!testimonial.submittedAt) {
          updates.submittedAt = testimonial.createdAt || new Date();
          needsUpdate = true;
        }

        if (needsUpdate && !this.options.dryRun) {
          await Testimonial.findByIdAndUpdate(testimonial._id, updates);
          updated++;
          this.log(`Updated testimonial from: ${testimonial.customerName}`);
        }
      }

      this.log(`Testimonial migration completed. Updated ${updated} testimonials.`);
      return { success: true, updated };

    } catch (error) {
      this.log(`Testimonial migration failed: ${error}`, 'error');
      throw error;
    }
  }

  // Migration: Clean up duplicate content
  async removeDuplicateContent() {
    this.log('Starting duplicate content cleanup...');
    
    try {
      let removed = 0;

      // Remove duplicate tours by slug
      const tourSlugs = await Tour.distinct('slug');
      for (const slug of tourSlugs) {
        const duplicates = await Tour.find({ slug }).sort({ createdAt: 1 });
        if (duplicates.length > 1) {
          // Keep the first one, remove the rest
          for (let i = 1; i < duplicates.length; i++) {
            if (!this.options.dryRun) {
              await Tour.findByIdAndDelete(duplicates[i]._id);
            }
            removed++;
            this.log(`Removed duplicate tour: ${duplicates[i].title}`);
          }
        }
      }

      // Remove duplicate blog posts by slug
      const blogSlugs = await BlogPost.distinct('slug');
      for (const slug of blogSlugs) {
        const duplicates = await BlogPost.find({ slug }).sort({ createdAt: 1 });
        if (duplicates.length > 1) {
          for (let i = 1; i < duplicates.length; i++) {
            if (!this.options.dryRun) {
              await BlogPost.findByIdAndDelete(duplicates[i]._id);
            }
            removed++;
            this.log(`Removed duplicate blog post: ${duplicates[i].title}`);
          }
        }
      }

      this.log(`Duplicate cleanup completed. Removed ${removed} items.`);
      return { success: true, removed };

    } catch (error) {
      this.log(`Duplicate cleanup failed: ${error}`, 'error');
      throw error;
    }
  }

  // Migration: Update content structure
  async updateContentStructure() {
    this.log('Starting content structure update...');
    
    try {
      let updated = 0;

      // Update tour structure
      const tours = await Tour.find({});
      for (const tour of tours) {
        const updates: Record<string, unknown> = {};
        let needsUpdate = false;

        // Ensure all required fields exist
        if (!tour.highlights || !Array.isArray(tour.highlights)) {
          updates.highlights = [];
          needsUpdate = true;
        }

        if (!tour.included || !Array.isArray(tour.included)) {
          updates.included = [];
          needsUpdate = true;
        }

        if (!tour.excluded || !Array.isArray(tour.excluded)) {
          updates.excluded = [];
          needsUpdate = true;
        }

        // Ensure price structure is correct
        if (!tour.price || typeof tour.price !== 'object') {
          updates.price = {
            amount: 0,
            currency: 'USD',
            priceType: 'per_person'
          };
          needsUpdate = true;
        }

        if (needsUpdate && !this.options.dryRun) {
          await Tour.findByIdAndUpdate(tour._id, updates);
          updated++;
          this.log(`Updated structure for tour: ${tour.title}`);
        }
      }

      this.log(`Content structure update completed. Updated ${updated} items.`);
      return { success: true, updated };

    } catch (error) {
      this.log(`Content structure update failed: ${error}`, 'error');
      throw error;
    }
  }

  // Run all migrations
  async runAllMigrations() {
    this.log('Starting all content migrations...');
    
    const results = {
      seoFields: { success: false, updated: 0 },
      imagePaths: { success: false, updated: 0 },
      testimonialFields: { success: false, updated: 0 },
      duplicateCleanup: { success: false, removed: 0 },
      contentStructure: { success: false, updated: 0 }
    };

    try {
      await connectDB();
      this.log('Connected to database');

      results.seoFields = await this.addSEOFieldsToTours();
      results.imagePaths = await this.migrateImagePaths();
      results.testimonialFields = await this.addMissingTestimonialFields();
      results.duplicateCleanup = await this.removeDuplicateContent();
      results.contentStructure = await this.updateContentStructure();

      this.log('All migrations completed successfully!');
      return results;

    } catch (error) {
      this.log(`Migration failed: ${error}`, 'error');
      throw error;
    }
  }
}

// CLI interface
async function runMigrations() {
  const args = process.argv.slice(2);
  const options: MigrationOptions = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose'),
    batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1] || '100')
  };

  const migrator = new ContentMigrator(options);

  try {
    if (args.includes('--seo-fields')) {
      await migrator.addSEOFieldsToTours();
    } else if (args.includes('--image-paths')) {
      await migrator.migrateImagePaths();
    } else if (args.includes('--testimonial-fields')) {
      await migrator.addMissingTestimonialFields();
    } else if (args.includes('--remove-duplicates')) {
      await migrator.removeDuplicateContent();
    } else if (args.includes('--content-structure')) {
      await migrator.updateContentStructure();
    } else {
      // Run all migrations
      await migrator.runAllMigrations();
    }

    console.log('✅ Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runMigrations();
}

export default ContentMigrator;
export { ContentMigrator, MigrationOptions };
