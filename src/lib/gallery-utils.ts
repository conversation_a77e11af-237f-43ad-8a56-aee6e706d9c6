// Map of image filenames to their metadata
const imageMetadata: Record<string, { 
  title: string; 
  category: 'destinations' | 'activities' | 'testimonials' | 'tours' | 'culture' | 'wildlife';
  tags: string[];
  description?: string;
}> = {
  'Elephants Wading in Water Under Tree-735-X-470.jpg': {
    title: 'Elephants in Water',
    category: 'wildlife',
    tags: ['elephants', 'wildlife', 'sri lanka'],
    description: 'A group of elephants wading in water under a tree'
  },
  'Family Safari Tour Group in Vehicle-1200-X-1600.jpg': {
    title: 'Family Safari Adventure',
    category: 'tours',
    tags: ['safari', 'family', 'wildlife', 'jeep'],
    description: 'Family enjoying a safari tour in a vehicle'
  },
  'Family at Gangaramaya Temple Sri Lanka-1600-X-1200.jpg': {
    title: 'Gangaramaya Temple Visit',
    category: 'culture',
    tags: ['temple', 'family', 'culture', 'colombo'],
    description: 'Family exploring Gangaramaya Temple in Colombo'
  },
  'Family at Temple of the Tooth Sri Lanka-1200-X-1600.jpg': {
    title: 'Temple of the Sacred Tooth',
    category: 'culture',
    tags: ['temple', 'kandy', 'buddhism', 'unesco'],
    description: 'Family visiting the sacred Temple of the Tooth in Kandy'
  },
  'Family on Safari Jeepjpg-1600-X-1200.jpg': {
    title: 'Safari Jeep Tour',
    category: 'tours',
    tags: ['safari', 'jeep', 'wildlife', 'family'],
    description: 'Family on a safari jeep tour'
  },
  'GarrisonsTours_Train_Bridge_Travel-940-X-788.jpg': {
    title: 'Scenic Train Journey',
    category: 'destinations',
    tags: ['train', 'scenic', 'journey', 'landscape'],
    description: 'Scenic train journey through Sri Lankan countryside'
  },
  'Knuckles-Trekking-Sri-Lanka-Tourjpg-1414-X-2000.jpg': {
    title: 'Knuckles Mountain Trek',
    category: 'activities',
    tags: ['trekking', 'mountains', 'adventure', 'nature'],
    description: 'Trekking in the Knuckles Mountain Range'
  },
  'Sri Lanka Ancient Ruin Tour Advertisement-1448-X-2048.jpg': {
    title: 'Ancient Ruins Exploration',
    category: 'culture',
    tags: ['ancient', 'ruins', 'history', 'archaeology'],
    description: 'Exploring ancient Sri Lankan ruins'
  },
  'Sri Lanka Tea Plantation Tour Adjpg-940-X-788.jpg': {
    title: 'Tea Plantation Tour',
    category: 'tours',
    tags: ['tea', 'plantations', 'hills', 'culture'],
    description: 'Tour of a traditional Sri Lankan tea plantation'
  },
  'Sri Lanka Tour Advertisement Imagejpg-904-X-1280.jpg': {
    title: 'Sri Lanka Scenic Beauty',
    category: 'destinations',
    tags: ['landscape', 'nature', 'scenic'],
    description: 'Beautiful landscapes of Sri Lanka'
  },
  'Sri Lanka Tours Advertisementjpg-940-X-788.jpg': {
    title: 'Tour Highlights',
    category: 'tours',
    tags: ['highlights', 'destinations', 'attractions'],
    description: 'Highlights of our Sri Lanka tours'
  },
  'Sri Lanka Train on Nine Arch Bridge Ad-2048-X-1717.jpg': {
    title: 'Nine Arch Bridge',
    category: 'destinations',
    tags: ['bridge', 'train', 'scenic', 'architecture'],
    description: 'Famous Nine Arch Bridge in Ella'
  },
  'Sri Lanka Wild Tours Adjpg-2048-X-1717.jpg': {
    title: 'Wildlife Tours',
    category: 'tours',
    tags: ['wildlife', 'safari', 'nature', 'animals'],
    description: 'Experience Sri Lanka\'s incredible wildlife'
  },
  'Sri-Lanka-Romantic-Getaway-Tour-Packagejpg-1414-X-2000.jpg': {
    title: 'Romantic Getaway',
    category: 'tours',
    tags: ['romantic', 'couples', 'luxury', 'beach'],
    description: 'Romantic getaway packages in Sri Lanka'
  },
  'Sri-Lanka-Train-Tourjpg-1175-X-985.jpg': {
    title: 'Picturesque Train Ride',
    category: 'activities',
    tags: ['train', 'scenic', 'journey', 'hills'],
    description: 'Scenic train ride through the hills'
  },
  'Sri-Lanka-Tuk-Tuk-Tourjpg-940-X-788.jpg': {
    title: 'Tuk-Tuk Adventure',
    category: 'activities',
    tags: ['tuk-tuk', 'city', 'tour', 'local'],
    description: 'Explore the city by tuk-tuk'
  },
  'Temple Tour Guide Explaining Sri Lankan Shrine-1280-X-720.jpg': {
    title: 'Temple Tour',
    category: 'culture',
    tags: ['temple', 'guide', 'religion', 'history'],
    description: 'Guided tour of a Sri Lankan temple'
  }
};

export interface GalleryImage {
  _id: string;
  title: string;
  description?: string;
  imageUrl: string;
  thumbnailUrl?: string;
  category: string;
  location?: string;
  tags: string[];
  createdAt: string;
}

export function getPublicImages(): GalleryImage[] {
  return Object.entries(imageMetadata).map(([filename, meta], index) => ({
    _id: `public-${index + 1}`,
    title: meta.title,
    description: meta.description,
    imageUrl: `/${filename}`,
    thumbnailUrl: `/${filename}`,
    category: meta.category,
    tags: meta.tags,
    createdAt: new Date().toISOString()
  }));
}

export function getUniqueCategories(images: GalleryImage[]): string[] {
  const categories = new Set(images.map(img => img.category));
  return Array.from(categories).sort();
}

export function filterImages(
  images: GalleryImage[], 
  searchTerm: string, 
  category: string
): GalleryImage[] {
  return images.filter(img => {
    const matchesSearch = !searchTerm || 
      img.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      img.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = !category || category === 'all' || img.category === category;
    
    return matchesSearch && matchesCategory;
  });
}
