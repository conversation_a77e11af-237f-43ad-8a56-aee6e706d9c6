import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import * as bcrypt from 'bcrypt';
import { securityHeaders } from './security-headers';

// Security headers configuration
// Re-export security headers from separate module for Edge Runtime compatibility
export { securityHeaders } from './security-headers';

// Rate limiting configuration for Next.js middleware
export const createRateLimit = (options: {
  windowMs?: number;
  max?: number;
  message?: string;
}) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return {
    windowMs: options.windowMs || 1 * 60 * 1000, // 15 minutes
    max: options.max || 100,
    message: options.message || 'Too many requests from this IP, please try again later.',
    check: (ip: string) => {
      const now = Date.now();
      const windowMs = options.windowMs || 1 * 60 * 1000;
      const max = options.max || 100;
      
      const record = requests.get(ip);
      
      if (!record || now > record.resetTime) {
        requests.set(ip, { count: 1, resetTime: now + windowMs });
        return { allowed: true, remaining: max - 1 };
      }
      
      if (record.count >= max) {
        return { allowed: false, remaining: 0 };
      }
      
      record.count++;
      return { allowed: true, remaining: max - record.count };
    }
  };
};

// Slow down configuration for Next.js middleware
export const createSlowDown = (options: {
  windowMs?: number;
  delayAfter?: number;
  delayMs?: number;
}) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return {
    windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes
    delayAfter: options.delayAfter || 5,
    delayMs: options.delayMs || 500,
    getDelay: (ip: string) => {
      const now = Date.now();
      const windowMs = options.windowMs || 15 * 60 * 1000;
      const delayAfter = options.delayAfter || 5;
      const delayMs = options.delayMs || 500;
      
      const record = requests.get(ip);
      
      if (!record || now > record.resetTime) {
        requests.set(ip, { count: 1, resetTime: now + windowMs });
        return 0;
      }
      
      record.count++;
      
      if (record.count <= delayAfter) {
        return 0;
      }
      
      return (record.count - delayAfter) * delayMs;
    }
  };
};

// Input validation schemas
export const validationSchemas = {
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
  ),
  name: z.string().min(1).max(100).regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format'),
  message: z.string().min(1).max(2000),
  slug: z.string().regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  url: z.string().url().optional().or(z.literal('')),
  mongoId: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId'),
};

// Password hashing utilities
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
};

export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

// CSRF token generation and validation using Web Crypto API
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const validateCSRFToken = (token: string, sessionToken: string): boolean => {
  if (token.length !== sessionToken.length) {
    return false;
  }
  
  const encoder = new TextEncoder();
  const tokenBytes = encoder.encode(token);
  const sessionBytes = encoder.encode(sessionToken);
  
  let result = 0;
  for (let i = 0; i < tokenBytes.length; i++) {
    result |= tokenBytes[i] ^ sessionBytes[i];
  }
  
  return result === 0;
};

// Data sanitization
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

export const sanitizeObject = (obj: Record<string, unknown>): Record<string, unknown> => {
  const sanitized: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value);
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key] = sanitizeObject(value as Record<string, unknown>);
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeInput(item) : 
        typeof item === 'object' && item !== null && !Array.isArray(item) ? sanitizeObject(item as Record<string, unknown>) : item
      );
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

// SQL injection prevention (for MongoDB)
export const sanitizeMongoQuery = (query: unknown): unknown => {
  if (typeof query !== 'object' || query === null) {
    return query;
  }
  
  const sanitized: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(query)) {
    // Remove potentially dangerous operators
    if (key.startsWith('$') && !['$eq', '$ne', '$gt', '$gte', '$lt', '$lte', '$in', '$nin', '$regex'].includes(key)) {
      continue;
    }
    
    if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeMongoQuery(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

// Request validation middleware
export const validateRequest = (schema: z.ZodSchema) => {
  return async (req: NextRequest) => {
    try {
      const body = await req.json();
      const sanitizedBody = sanitizeObject(body);
      const validatedData = schema.parse(sanitizedBody);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { 
          success: false, 
          error: 'Validation failed', 
          details: error.issues.map(e => ({ field: e.path.join('.'), message: e.message }))
        };
      }
      return { success: false, error: 'Invalid request format' };
    }
  };
};

// Session security using Web Crypto API
export const generateSecureSessionId = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const isSecureContext = (req: NextRequest): boolean => {
  return req.url.startsWith('https://') || req.headers.get('x-forwarded-proto') === 'https';
};

// File upload security
export const validateFileUpload = (file: File, options: {
  maxSize?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
}) => {
  const { maxSize = 5 * 1024 * 1024, allowedTypes = [], allowedExtensions = [] } = options;
  
  // Check file size
  if (file.size > maxSize) {
    return { valid: false, error: `File size exceeds ${maxSize / (1024 * 1024)}MB limit` };
  }
  
  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return { valid: false, error: `File type ${file.type} is not allowed` };
  }
  
  // Check file extension
  if (allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      return { valid: false, error: `File extension .${extension} is not allowed` };
    }
  }
  
  return { valid: true };
};

// API response helpers with security headers
export const createSecureResponse = (data: unknown, status = 200) => {
  const response = NextResponse.json(data, { status });
  
  // Add security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value as string);
  });
  
  return response;
};

export const createErrorResponse = (message: string, status = 400, details?: unknown) => {
  const errorData: Record<string, unknown> = { error: message };
  if (details && process.env.NODE_ENV === 'development') {
    errorData.details = details;
  }
  
  return createSecureResponse(errorData, status);
};

// Environment-specific security configurations
export const getSecurityConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    cookieOptions: {
      httpOnly: true,
      secure: isProduction,
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
    sessionOptions: {
      secret: process.env.NEXTAUTH_SECRET || 'fallback-secret-key',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: isProduction,
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      },
    },
    corsOptions: {
      origin: isProduction ? ['https://garrisonstours.com', 'https://www.garrisonstours.com'] : true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'],
    },
  };
};

// Audit logging
export const logSecurityEvent = (event: {
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'data_access' | 'data_modification';
  userId?: string;
  ip?: string;
  userAgent?: string;
  details?: unknown;
}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    ...event,
  };
  
  // In production, you would send this to a logging service
  if (process.env.NODE_ENV === 'development') {
    console.log('Security Event:', logEntry);
  }
  
  // TODO: Implement proper logging service integration
  // Examples: Winston, Pino, or cloud logging services
};

const securityUtils = {
  securityHeaders,
  createRateLimit,
  createSlowDown,
  validationSchemas,
  hashPassword,
  verifyPassword,
  generateCSRFToken,
  validateCSRFToken,
  sanitizeInput,
  sanitizeObject,
  sanitizeMongoQuery,
  validateRequest,
  generateSecureSessionId,
  isSecureContext,
  validateFileUpload,
  createSecureResponse,
  createErrorResponse,
  getSecurityConfig,
  logSecurityEvent,
};

export default securityUtils;
