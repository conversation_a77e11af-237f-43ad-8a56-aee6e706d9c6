import mongoose, { Document, Schema } from 'mongoose';

export interface ItineraryDay {
  day: number;
  title: string;
  description: string;
  activities: string[];
  accommodation?: string;
  meals: ('breakfast' | 'lunch' | 'dinner')[];
}

export interface ITour extends Document {
  title: string;
  slug: string;
  description: string;
  shortDescription: string;
  itinerary: ItineraryDay[];
  price: {
    amount: number;
    currency: 'USD' | 'EUR';
    priceType: 'per_person' | 'per_group';
  };
  duration: {
    days: number;
    nights: number;
  };
  images: {
    featured: string;
    gallery: string[];
    alt: string[];
  };
  category: 'cultural' | 'wildlife' | 'adventure' | 'romantic' | 'family';
  highlights: string[];
  included: string[];
  excluded: string[];
  difficulty: 'easy' | 'moderate' | 'challenging';
  maxGroupSize: number;
  isActive: boolean;
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

const ItineraryDaySchema = new Schema<ItineraryDay>({
  day: { type: Number, required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  activities: [{ type: String }],
  accommodation: { type: String },
  meals: [{
    type: String,
    enum: ['breakfast', 'lunch', 'dinner']
  }]
});

const TourSchema = new Schema<ITour>({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String, required: true },
  shortDescription: { type: String, required: true },
  itinerary: [ItineraryDaySchema],
  price: {
    amount: { type: Number, required: true },
    currency: { type: String, enum: ['USD', 'EUR'], default: 'USD' },
    priceType: { type: String, enum: ['per_person', 'per_group'], default: 'per_person' }
  },
  duration: {
    days: { type: Number, required: true },
    nights: { type: Number, required: true }
  },
  images: {
    featured: { type: String, required: true },
    gallery: [{ type: String }],
    alt: [{ type: String }]
  },
  category: {
    type: String,
    enum: ['cultural', 'wildlife', 'adventure', 'romantic', 'family'],
    required: true
  },
  highlights: [{ type: String }],
  included: [{ type: String }],
  excluded: [{ type: String }],
  difficulty: {
    type: String,
    enum: ['easy', 'moderate', 'challenging'],
    default: 'moderate'
  },
  maxGroupSize: { type: Number, default: 12 },
  isActive: { type: Boolean, default: true },
  seoMeta: {
    title: { type: String },
    description: { type: String },
    keywords: [{ type: String }]
  }
}, {
  timestamps: true
});

// Create indexes for better performance
TourSchema.index({ slug: 1 });
TourSchema.index({ category: 1 });
TourSchema.index({ isActive: 1 });
TourSchema.index({ 'price.amount': 1 });

export default mongoose.models.Tour || mongoose.model<ITour>('Tour', TourSchema);