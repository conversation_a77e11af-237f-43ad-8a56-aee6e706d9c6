import Script from 'next/script';

interface OrganizationData {
  name: string;
  url: string;
  logo: string;
  description: string;
  address: {
    streetAddress: string;
    addressLocality: string;
    addressCountry: string;
  };
  contactPoint: {
    telephone: string;
    email: string;
  };
  sameAs: string[];
}

interface TourData {
  name: string;
  description: string;
  url: string;
  image: string[];
  price: {
    amount: number;
    currency: string;
  };
  duration: string;
  location: string;
  provider: {
    name: string;
    url: string;
  };
}

interface BlogPostData {
  headline: string;
  description: string;
  url: string;
  image: string;
  author: {
    name: string;
  };
  publisher: {
    name: string;
    logo: string;
  };
  datePublished: string;
  dateModified: string;
}

interface TestimonialData {
  author: {
    name: string;
    address: {
      addressCountry: string;
    };
  };
  reviewRating: {
    ratingValue: number;
    bestRating: number;
  };
  reviewBody: string;
  itemReviewed: {
    name: string;
    url: string;
  };
  datePublished: string;
}

interface StructuredDataProps {
  type: 'organization' | 'tour' | 'blogPost' | 'testimonial';
  data: OrganizationData | TourData | BlogPostData | TestimonialData;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const generateStructuredData = () => {
    switch (type) {
      case 'organization':
        const orgData = data as OrganizationData;
        return {
          '@context': 'https://schema.org',
          '@type': 'TravelAgency',
          name: orgData.name,
          url: orgData.url,
          logo: orgData.logo,
          description: orgData.description,
          address: {
            '@type': 'PostalAddress',
            streetAddress: orgData.address.streetAddress,
            addressLocality: orgData.address.addressLocality,
            addressCountry: orgData.address.addressCountry,
          },
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: orgData.contactPoint.telephone,
            email: orgData.contactPoint.email,
            contactType: 'customer service',
          },
          sameAs: orgData.sameAs,
        };

      case 'tour':
        const tourData = data as TourData;
        return {
          '@context': 'https://schema.org',
          '@type': 'TouristTrip',
          name: tourData.name,
          description: tourData.description,
          url: tourData.url,
          image: tourData.image,
          offers: {
            '@type': 'Offer',
            price: tourData.price.amount,
            priceCurrency: tourData.price.currency,
            availability: 'https://schema.org/InStock',
          },
          duration: tourData.duration,
          touristType: 'https://schema.org/Tourist',
          itinerary: {
            '@type': 'Place',
            name: tourData.location,
          },
          provider: {
            '@type': 'TravelAgency',
            name: tourData.provider.name,
            url: tourData.provider.url,
          },
        };

      case 'blogPost':
        const blogData = data as BlogPostData;
        return {
          '@context': 'https://schema.org',
          '@type': 'BlogPosting',
          headline: blogData.headline,
          description: blogData.description,
          url: blogData.url,
          image: blogData.image,
          author: {
            '@type': 'Person',
            name: blogData.author.name,
          },
          publisher: {
            '@type': 'Organization',
            name: blogData.publisher.name,
            logo: {
              '@type': 'ImageObject',
              url: blogData.publisher.logo,
            },
          },
          datePublished: blogData.datePublished,
          dateModified: blogData.dateModified,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': blogData.url,
          },
        };

      case 'testimonial':
        const testimonialData = data as TestimonialData;
        return {
          '@context': 'https://schema.org',
          '@type': 'Review',
          author: {
            '@type': 'Person',
            name: testimonialData.author.name,
            address: {
              '@type': 'PostalAddress',
              addressCountry: testimonialData.author.address.addressCountry,
            },
          },
          reviewRating: {
            '@type': 'Rating',
            ratingValue: testimonialData.reviewRating.ratingValue,
            bestRating: testimonialData.reviewRating.bestRating,
          },
          reviewBody: testimonialData.reviewBody,
          itemReviewed: {
            '@type': 'TouristTrip',
            name: testimonialData.itemReviewed.name,
            url: testimonialData.itemReviewed.url,
          },
          datePublished: testimonialData.datePublished,
        };

      default:
        return null;
    }
  };

  const structuredData = generateStructuredData();

  if (!structuredData) {
    return null;
  }

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// Helper function to generate organization structured data for the main site
export function OrganizationStructuredData() {
  const organizationData: OrganizationData = {
    name: 'Garrisons Tours',
    url: 'https://garrisonstours.com',
    logo: 'https://garrisonstours.com/logo.png',
    description: 'Premium Sri Lankan tour operator offering authentic cultural, wildlife, and adventure experiences across the Pearl of the Indian Ocean.',
    address: {
      streetAddress: '123 Galle Road',
      addressLocality: 'Colombo 03',
      addressCountry: 'Sri Lanka',
    },
    contactPoint: {
      telephone: '+94-77-123-4567',
      email: '<EMAIL>',
    },
    sameAs: [
      'https://www.facebook.com/garrisonstours',
      'https://www.instagram.com/garrisonstours',
      'https://www.twitter.com/garrisonstours',
    ],
  };

  return <StructuredData type="organization" data={organizationData} />;
}

// Helper function to generate tour structured data
export function TourStructuredData({ tour }: { tour: Record<string, unknown> }) {
  const tourData: TourData = {
    name: tour.title,
    description: tour.shortDescription,
    url: `https://garrisonstours.com/tours/${tour.slug}`,
    image: tour.images?.gallery || [tour.images?.featured || ''],
    price: {
      amount: tour.price.amount,
      currency: tour.price.currency,
    },
    duration: `P${tour.duration.days}D`,
    location: 'Sri Lanka',
    provider: {
      name: 'Garrisons Tours',
      url: 'https://garrisonstours.com',
    },
  };

  return <StructuredData type="tour" data={tourData} />;
}

// Helper function to generate blog post structured data
export function BlogPostStructuredData({ post }: { post: Record<string, unknown> }) {
  const blogData: BlogPostData = {
    headline: post.title,
    description: post.excerpt,
    url: `https://garrisonstours.com/blog/${post.slug}`,
    image: post.featuredImage || 'https://garrisonstours.com/default-blog-image.jpg',
    author: {
      name: post.author.name,
    },
    publisher: {
      name: 'Garrisons Tours',
      logo: 'https://garrisonstours.com/logo.png',
    },
    datePublished: post.publishedAt,
    dateModified: post.updatedAt,
  };

  return <StructuredData type="blogPost" data={blogData} />;
}

// Helper function to generate testimonial structured data
export function TestimonialStructuredData({ testimonial }: { testimonial: Record<string, unknown> }) {
  const testimonialData: TestimonialData = {
    author: {
      name: testimonial.customerName,
      address: {
        addressCountry: testimonial.customerCountry,
      },
    },
    reviewRating: {
      ratingValue: testimonial.rating,
      bestRating: 5,
    },
    reviewBody: testimonial.content,
    itemReviewed: {
      name: testimonial.tourTitle || 'Sri Lanka Tour Experience',
      url: testimonial.tourId?.slug 
        ? `https://garrisonstours.com/tours/${testimonial.tourId.slug}`
        : 'https://garrisonstours.com/tours',
    },
    datePublished: testimonial.submittedAt,
  };

  return <StructuredData type="testimonial" data={testimonialData} />;
}
