import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { securityHeaders } from '@/lib/security';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Simple rate limiting implementation
function rateLimit(ip: string, limit: number = 100, windowMs: number = 15 * 60 * 1000) {
  const now = Date.now();
  const key = `${ip}`;
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return { allowed: true, remaining: limit - 1 };
  }

  if (record.count >= limit) {
    return { allowed: false, remaining: 0 };
  }

  record.count++;
  return { allowed: true, remaining: limit - record.count };
}

// Clean up expired rate limit records
setInterval(() => {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();

  // Add security headers to all responses
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Get client IP
  const ip = request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown';

  // Apply rate limiting
  const rateLimitResult = rateLimit(ip);
  
  if (!rateLimitResult.allowed) {
    return new NextResponse('Too Many Requests', {
      status: 429,
      headers: {
        'Retry-After': '900', // 15 minutes
        ...Object.fromEntries(Object.entries(securityHeaders)),
      },
    });
  }

  // Add rate limit headers
  response.headers.set('X-RateLimit-Limit', '100');
  response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());

  // Protect admin routes
  if (pathname.startsWith('/admin')) {
    const token = await getToken({ req: request });
    
    if (!token) {
      // Redirect to login page
      const loginUrl = new URL('/admin/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Check if user has admin role
    if (token.role !== 'admin' && token.role !== 'editor') {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  // Protect API routes that require authentication
  if (pathname.startsWith('/api/admin')) {
    const token = await getToken({ req: request });
    
    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    if (token.role !== 'admin' && token.role !== 'editor') {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  // Apply stricter rate limiting for sensitive endpoints
  if (pathname.startsWith('/api/auth') || pathname.startsWith('/api/contact') || pathname.startsWith('/api/bookings')) {
    const strictRateLimit = rateLimit(`${ip}-sensitive`, 10, 15 * 60 * 1000); // 10 requests per 15 minutes
    
    if (!strictRateLimit.allowed) {
      return new NextResponse('Too Many Requests', {
        status: 429,
        headers: {
          'Retry-After': '900',
          ...Object.fromEntries(Object.entries(securityHeaders)),
        },
      });
    }
  }

  // Block suspicious requests
  const userAgent = request.headers.get('user-agent') || '';
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ];

  // Allow legitimate bots but block suspicious ones
  const isLegitimateBot = /googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|linkedinbot/i.test(userAgent);
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent)) && !isLegitimateBot;

  if (isSuspicious && (pathname.startsWith('/admin') || pathname.startsWith('/api/admin'))) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Prevent access to sensitive files
  const sensitiveFiles = [
    '.env',
    '.env.local',
    '.env.production',
    'package.json',
    'package-lock.json',
    'yarn.lock',
    '.git',
    '.gitignore',
    'README.md',
  ];

  if (sensitiveFiles.some(file => pathname.includes(file))) {
    return new NextResponse('Not Found', { status: 404 });
  }

  // Add CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    const origin = request.headers.get('origin');
    const allowedOrigins = process.env.NODE_ENV === 'production' 
      ? ['https://garrisonstours.com', 'https://www.garrisonstours.com']
      : ['http://localhost:3000'];

    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }

    response.headers.set('Access-Control-Allow-Credentials', 'true');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token');

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, { status: 200, headers: response.headers });
    }
  }

  // Log security events for monitoring
  if (process.env.NODE_ENV === 'production') {
    // Log failed authentication attempts
    if (pathname.startsWith('/api/auth') && request.method === 'POST') {
      console.log(`Auth attempt from IP: ${ip}, User-Agent: ${userAgent}`);
    }

    // Log admin access
    if (pathname.startsWith('/admin') || pathname.startsWith('/api/admin')) {
      console.log(`Admin access from IP: ${ip}, Path: ${pathname}`);
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
