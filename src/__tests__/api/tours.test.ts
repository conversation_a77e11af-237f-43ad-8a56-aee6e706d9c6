import { NextRequest } from 'next/server'
import { GET } from '@/app/api/tours/route'

// Mock MongoDB connection
jest.mock('@/lib/mongodb', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue(true),
}))

// Mock Tour model
const mockTours = [
  {
    _id: '507f1f77bcf86cd799439011',
    title: 'Cultural Triangle Explorer',
    slug: 'cultural-triangle-explorer',
    shortDescription: 'Explore ancient temples and historical sites',
    category: 'cultural',
    difficulty: 'moderate',
    duration: { days: 7, nights: 6 },
    price: { amount: 899, currency: 'USD', priceType: 'per_person' },
    maxGroupSize: 12,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '507f1f77bcf86cd799439012',
    title: 'Wildlife Safari Adventure',
    slug: 'wildlife-safari-adventure',
    shortDescription: 'Experience Sri Lankan wildlife in national parks',
    category: 'wildlife',
    difficulty: 'easy',
    duration: { days: 5, nights: 4 },
    price: { amount: 699, currency: 'USD', priceType: 'per_person' },
    maxGroupSize: 8,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

jest.mock('@/models/Tour', () => ({
  __esModule: true,
  default: {
    find: jest.fn(),
    countDocuments: jest.fn(),
  },
}))

import Tour from '@/models/Tour'

const mockTourModel = Tour as jest.Mocked<typeof Tour>

describe('/api/tours', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/tours', () => {
    it('returns tours successfully', async () => {
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(mockTours),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(2)

      const request = new NextRequest('http://localhost:3000/api/tours')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.tours).toHaveLength(2)
      expect(data.tours[0].title).toBe('Cultural Triangle Explorer')
      expect(data.totalPages).toBe(1)
      expect(data.currentPage).toBe(1)
    })

    it('handles pagination correctly', async () => {
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue([mockTours[0]]),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(2)

      const request = new NextRequest('http://localhost:3000/api/tours?page=1&limit=1')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.tours).toHaveLength(1)
      expect(data.totalPages).toBe(2)
      expect(data.currentPage).toBe(1)
    })

    it('filters by category', async () => {
      const culturalTours = [mockTours[0]]
      
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(culturalTours),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/tours?category=cultural')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.tours).toHaveLength(1)
      expect(data.tours[0].category).toBe('cultural')
    })

    it('handles search queries', async () => {
      const searchResults = [mockTours[0]]
      
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(searchResults),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/tours?search=cultural')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.tours).toHaveLength(1)
    })

    it('handles database errors', async () => {
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockRejectedValue(new Error('Database error')),
            }),
          }),
        }),
      } as unknown)

      const request = new NextRequest('http://localhost:3000/api/tours')
      const response = await GET(request)

      expect(response.status).toBe(500)
    })

    it('validates query parameters', async () => {
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue([]),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(0)

      // Test with invalid page number
      const request = new NextRequest('http://localhost:3000/api/tours?page=0')
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.currentPage).toBe(1) // Should default to 1
    })

    it('returns only active tours by default', async () => {
      mockTourModel.find.mockReturnValue({
        select: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(mockTours),
            }),
          }),
        }),
      } as unknown)

      mockTourModel.countDocuments.mockResolvedValue(2)

      const request = new NextRequest('http://localhost:3000/api/tours')
      await GET(request)

      // Verify that the find method was called with isActive: true
      expect(mockTourModel.find).toHaveBeenCalledWith({ isActive: true })
    })
  })
})
