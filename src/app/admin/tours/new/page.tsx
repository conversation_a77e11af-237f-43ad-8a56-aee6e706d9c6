'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/components/admin/AdminLayout';
import { 
  Save, 
  ArrowLeft, 
  Plus, 
  X,
  Upload,
  Eye
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';

interface TourFormData {
  title: string;
  slug: string;
  shortDescription: string;
  description: string;
  category: string;
  difficulty: string;
  duration: {
    days: number;
    nights: number;
  };
  price: {
    amount: number;
    currency: string;
    priceType: string;
  };
  maxGroupSize: number;
  highlights: string[];
  included: string[];
  excluded: string[];
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    activities: string[];
    accommodation?: string;
    meals: string[];
  }>;
  images: {
    featured: string;
    gallery: string[];
    alt: string[];
  };
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  isActive: boolean;
}

export default function NewTourPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentTab, setCurrentTab] = useState('basic');
  
  const [formData, setFormData] = useState<TourFormData>({
    title: '',
    slug: '',
    shortDescription: '',
    description: '',
    category: 'cultural',
    difficulty: 'moderate',
    duration: {
      days: 7,
      nights: 6
    },
    price: {
      amount: 899,
      currency: 'USD',
      priceType: 'per_person'
    },
    maxGroupSize: 12,
    highlights: [''],
    included: [''],
    excluded: [''],
    itinerary: [{
      day: 1,
      title: '',
      description: '',
      activities: [''],
      accommodation: '',
      meals: []
    }],
    images: {
      featured: '',
      gallery: [],
      alt: []
    },
    seoMeta: {
      title: '',
      description: '',
      keywords: []
    },
    isActive: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title),
      seoMeta: {
        ...prev.seoMeta,
        title: title ? `${title} - Garrisons Tours` : ''
      }
    }));
  };

  const addArrayItem = (field: keyof TourFormData, value: any = '') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...(prev[field] as any[]), value]
    }));
  };

  const updateArrayItem = (field: keyof TourFormData, index: number, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as any[]).map((item, i) => i === index ? value : item)
    }));
  };

  const removeArrayItem = (field: keyof TourFormData, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as any[]).filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.shortDescription.trim()) newErrors.shortDescription = 'Short description is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (formData.price.amount <= 0) newErrors.price = 'Price must be greater than 0';
    if (formData.duration.days <= 0) newErrors.days = 'Duration must be at least 1 day';
    if (formData.maxGroupSize <= 0) newErrors.maxGroupSize = 'Group size must be at least 1';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      setCurrentTab('basic'); // Switch to basic tab to show errors
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/tours', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const tour = await response.json();
        router.push(`/admin/tours/${tour.slug}/edit?success=created`);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create tour');
      }
    } catch (error) {
      console.error('Error creating tour:', error);
      alert('Failed to create tour');
    } finally {
      setIsSubmitting(false);
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info' },
    { id: 'details', label: 'Details' },
    { id: 'itinerary', label: 'Itinerary' },
    { id: 'seo', label: 'SEO' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Create New Tour</h1>
              <p className="text-gray-600">Add a new tour package to your collection</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" disabled={isSubmitting}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={isSubmitting}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Tour
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setCurrentTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  currentTab === tab.id
                    ? 'border-emerald-500 text-emerald-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Form Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            {/* Basic Info Tab */}
            {currentTab === 'basic' && (
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="title">Tour Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => handleTitleChange(e.target.value)}
                      placeholder="e.g., Cultural Triangle Explorer"
                      className={errors.title ? 'border-red-500' : ''}
                    />
                    {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
                  </div>

                  <div>
                    <Label htmlFor="slug">URL Slug</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="cultural-triangle-explorer"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      URL: /tours/{formData.slug}
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="shortDescription">Short Description *</Label>
                    <Textarea
                      id="shortDescription"
                      value={formData.shortDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                      placeholder="Brief description for tour cards and previews"
                      rows={3}
                      className={errors.shortDescription ? 'border-red-500' : ''}
                    />
                    {errors.shortDescription && <p className="text-red-500 text-sm mt-1">{errors.shortDescription}</p>}
                  </div>

                  <div>
                    <Label htmlFor="description">Full Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Detailed description of the tour experience"
                      rows={6}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Category</Label>
                      <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cultural">Cultural</SelectItem>
                          <SelectItem value="wildlife">Wildlife</SelectItem>
                          <SelectItem value="adventure">Adventure</SelectItem>
                          <SelectItem value="romantic">Romantic</SelectItem>
                          <SelectItem value="family">Family</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Difficulty</Label>
                      <Select value={formData.difficulty} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="easy">Easy</SelectItem>
                          <SelectItem value="moderate">Moderate</SelectItem>
                          <SelectItem value="challenging">Challenging</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="days">Days *</Label>
                      <Input
                        id="days"
                        type="number"
                        min="1"
                        value={formData.duration.days}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          duration: { ...prev.duration, days: parseInt(e.target.value) || 1 }
                        }))}
                        className={errors.days ? 'border-red-500' : ''}
                      />
                      {errors.days && <p className="text-red-500 text-sm mt-1">{errors.days}</p>}
                    </div>

                    <div>
                      <Label htmlFor="nights">Nights</Label>
                      <Input
                        id="nights"
                        type="number"
                        min="0"
                        value={formData.duration.nights}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          duration: { ...prev.duration, nights: parseInt(e.target.value) || 0 }
                        }))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="maxGroupSize">Max Group Size *</Label>
                      <Input
                        id="maxGroupSize"
                        type="number"
                        min="1"
                        value={formData.maxGroupSize}
                        onChange={(e) => setFormData(prev => ({ ...prev, maxGroupSize: parseInt(e.target.value) || 1 }))}
                        className={errors.maxGroupSize ? 'border-red-500' : ''}
                      />
                      {errors.maxGroupSize && <p className="text-red-500 text-sm mt-1">{errors.maxGroupSize}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="price">Price (USD) *</Label>
                      <Input
                        id="price"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.price.amount}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          price: { ...prev.price, amount: parseFloat(e.target.value) || 0 }
                        }))}
                        className={errors.price ? 'border-red-500' : ''}
                      />
                      {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price}</p>}
                    </div>

                    <div>
                      <Label>Price Type</Label>
                      <Select 
                        value={formData.price.priceType} 
                        onValueChange={(value) => setFormData(prev => ({ 
                          ...prev, 
                          price: { ...prev.price, priceType: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="per_person">Per Person</SelectItem>
                          <SelectItem value="per_group">Per Group</SelectItem>
                          <SelectItem value="per_couple">Per Couple</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Details Tab */}
            {currentTab === 'details' && (
              <div className="space-y-6">
                {/* Highlights */}
                <Card>
                  <CardHeader>
                    <CardTitle>Tour Highlights</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {formData.highlights.map((highlight, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={highlight}
                            onChange={(e) => updateArrayItem('highlights', index, e.target.value)}
                            placeholder="Enter a tour highlight"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('highlights', index)}
                            disabled={formData.highlights.length === 1}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addArrayItem('highlights', '')}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Highlight
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Included */}
                <Card>
                  <CardHeader>
                    <CardTitle>What's Included</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {formData.included.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => updateArrayItem('included', index, e.target.value)}
                            placeholder="What's included in this tour"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('included', index)}
                            disabled={formData.included.length === 1}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addArrayItem('included', '')}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Excluded */}
                <Card>
                  <CardHeader>
                    <CardTitle>What's Not Included</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {formData.excluded.map((item, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={item}
                            onChange={(e) => updateArrayItem('excluded', index, e.target.value)}
                            placeholder="What's not included"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('excluded', index)}
                            disabled={formData.excluded.length === 1}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addArrayItem('excluded', '')}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Itinerary Tab */}
            {currentTab === 'itinerary' && (
              <Card>
                <CardHeader>
                  <CardTitle>Day by Day Itinerary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {formData.itinerary.map((day, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold">Day {day.day}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeArrayItem('itinerary', index)}
                            disabled={formData.itinerary.length === 1}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="space-y-4">
                          <div>
                            <Label>Day Title</Label>
                            <Input
                              value={day.title}
                              onChange={(e) => {
                                const newItinerary = [...formData.itinerary];
                                newItinerary[index] = { ...day, title: e.target.value };
                                setFormData(prev => ({ ...prev, itinerary: newItinerary }));
                              }}
                              placeholder="e.g., Arrival in Colombo"
                            />
                          </div>
                          
                          <div>
                            <Label>Description</Label>
                            <Textarea
                              value={day.description}
                              onChange={(e) => {
                                const newItinerary = [...formData.itinerary];
                                newItinerary[index] = { ...day, description: e.target.value };
                                setFormData(prev => ({ ...prev, itinerary: newItinerary }));
                              }}
                              placeholder="Describe what happens on this day"
                              rows={3}
                            />
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label>Accommodation</Label>
                              <Input
                                value={day.accommodation || ''}
                                onChange={(e) => {
                                  const newItinerary = [...formData.itinerary];
                                  newItinerary[index] = { ...day, accommodation: e.target.value };
                                  setFormData(prev => ({ ...prev, itinerary: newItinerary }));
                                }}
                                placeholder="Hotel or accommodation"
                              />
                            </div>
                            
                            <div>
                              <Label>Meals</Label>
                              <Input
                                value={day.meals.join(', ')}
                                onChange={(e) => {
                                  const newItinerary = [...formData.itinerary];
                                  newItinerary[index] = { 
                                    ...day, 
                                    meals: e.target.value.split(',').map(m => m.trim()).filter(m => m)
                                  };
                                  setFormData(prev => ({ ...prev, itinerary: newItinerary }));
                                }}
                                placeholder="Breakfast, Lunch, Dinner"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <Button
                      variant="outline"
                      onClick={() => addArrayItem('itinerary', {
                        day: formData.itinerary.length + 1,
                        title: '',
                        description: '',
                        activities: [''],
                        accommodation: '',
                        meals: []
                      })}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Day
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* SEO Tab */}
            {currentTab === 'seo' && (
              <Card>
                <CardHeader>
                  <CardTitle>SEO Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="seoTitle">SEO Title</Label>
                    <Input
                      id="seoTitle"
                      value={formData.seoMeta.title}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        seoMeta: { ...prev.seoMeta, title: e.target.value }
                      }))}
                      placeholder="SEO optimized title"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {formData.seoMeta.title.length}/60 characters
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="seoDescription">SEO Description</Label>
                    <Textarea
                      id="seoDescription"
                      value={formData.seoMeta.description}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        seoMeta: { ...prev.seoMeta, description: e.target.value }
                      }))}
                      placeholder="SEO meta description"
                      rows={3}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      {formData.seoMeta.description.length}/160 characters
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="seoKeywords">Keywords</Label>
                    <Input
                      id="seoKeywords"
                      value={formData.seoMeta.keywords.join(', ')}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        seoMeta: { 
                          ...prev.seoMeta, 
                          keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                        }
                      }))}
                      placeholder="keyword1, keyword2, keyword3"
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="isActive">Active (visible to customers)</Label>
                </div>
              </CardContent>
            </Card>

            {/* Images */}
            <Card>
              <CardHeader>
                <CardTitle>Images</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="featuredImage">Featured Image URL</Label>
                    <Input
                      id="featuredImage"
                      value={formData.images.featured}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        images: { ...prev.images, featured: e.target.value }
                      }))}
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                  
                  <Button variant="outline" size="sm" className="w-full">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Images
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <Badge className="text-xs">{formData.category}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span>{formData.duration.days}D/{formData.duration.nights}N</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Price:</span>
                    <span>${formData.price.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Group Size:</span>
                    <span>Max {formData.maxGroupSize}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
