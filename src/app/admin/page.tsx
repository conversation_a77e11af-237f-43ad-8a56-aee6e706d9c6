'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import AdminLayout from '@/components/admin/AdminLayout';
import { 
  MapPin, 
  Calendar, 
  MessageSquare, 
  Users, 
  TrendingUp, 
  Eye,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { Loading } from '@/components/ui/loading';

interface DashboardStats {
  tours: {
    total: number;
    active: number;
    draft: number;
  };
  bookings: {
    total: number;
    pending: number;
    confirmed: number;
    thisMonth: number;
  };
  testimonials: {
    total: number;
    approved: number;
    pending: number;
  };
  revenue: {
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'booking' | 'testimonial' | 'tour';
  title: string;
  description: string;
  timestamp: string;
  status: 'pending' | 'approved' | 'new';
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard statistics
      const [toursRes, bookingsRes, testimonialsRes] = await Promise.all([
        fetch('/api/tours?limit=1000'),
        fetch('/api/admin/bookings?limit=1000'),
        fetch('/api/admin/testimonials?limit=1000')
      ]);

      const [toursData, bookingsData, testimonialsData] = await Promise.all([
        toursRes.json(),
        bookingsRes.json(),
        testimonialsRes.json()
      ]);

      // Calculate stats
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const thisMonthBookings = bookingsData.bookings?.filter((booking: Record<string, unknown>) => {
        const bookingDate = new Date(booking.submittedAt as string);
        return bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear;
      }).length || 0;

      const dashboardStats: DashboardStats = {
        tours: {
          total: toursData.tours?.length || 0,
          active: toursData.tours?.filter((tour: Record<string, unknown>) => tour.isActive).length || 0,
          draft: toursData.tours?.filter((tour: Record<string, unknown>) => !tour.isActive).length || 0
        },
        bookings: {
          total: bookingsData.bookings?.length || 0,
          pending: bookingsData.statusCounts?.new || 0,
          confirmed: bookingsData.statusCounts?.confirmed || 0,
          thisMonth: thisMonthBookings
        },
        testimonials: {
          total: testimonialsData.testimonials?.length || 0,
          approved: testimonialsData.statusCounts?.approved || 0,
          pending: testimonialsData.statusCounts?.pending || 0
        },
        revenue: {
          thisMonth: thisMonthBookings * 850, // Estimated average booking value
          lastMonth: 12500, // Mock data
          growth: 15.2 // Mock data
        }
      };

      setStats(dashboardStats);

      // Create recent activity from bookings and testimonials
      const activities: RecentActivity[] = [
        ...(bookingsData.bookings?.slice(0, 3).map((booking: Record<string, unknown>) => ({
          id: booking._id as string,
          type: 'booking' as const,
          title: `New booking inquiry`,
          description: `${(booking.customer as Record<string, unknown>).name as string} - ${booking.tourTitle as string}`,
          timestamp: booking.submittedAt as string,
          status: booking.status === 'new' ? 'pending' as const : 'approved' as const
        })) || []),
        ...(testimonialsData.testimonials?.slice(0, 2).map((testimonial: Record<string, unknown>) => ({
          id: testimonial._id as string,
          type: 'testimonial' as const,
          title: `New testimonial`,
          description: `${testimonial.customerName as string} - ${testimonial.title as string}`,
          timestamp: testimonial.submittedAt as string,
          status: testimonial.isApproved ? 'approved' as const : 'pending' as const
        })) || [])
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);

      setRecentActivity(activities);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking': return Calendar;
      case 'testimonial': return MessageSquare;
      case 'tour': return MapPin;
      default: return Clock;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-amber-600 bg-amber-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'new': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <Loading text="Loading dashboard..." className="py-20" />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Welcome Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome back! Here's what's happening with your tours.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tours</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.tours.total || 0}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.tours.active || 0} active, {stats?.tours.draft || 0} draft
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bookings</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.bookings.total || 0}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.bookings.thisMonth || 0} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Testimonials</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.testimonials.total || 0}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.testimonials.pending || 0} pending approval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${stats?.revenue.thisMonth?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                +{stats?.revenue.growth || 0}% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.length > 0 ? (
                  recentActivity.map((activity) => {
                    const Icon = getActivityIcon(activity.type);
                    return (
                      <div key={activity.id} className="flex items-center space-x-4">
                        <div className="bg-gray-100 p-2 rounded-full">
                          <Icon className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-500 truncate">
                            {activity.description}
                          </p>
                        </div>
                        <div className="flex flex-col items-end">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                            {activity.status}
                          </span>
                          <span className="text-xs text-gray-500 mt-1">
                            {new Date(activity.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-gray-500 text-center py-4">No recent activity</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Button asChild className="h-20 flex-col">
                  <Link href="/admin/tours/new">
                    <MapPin className="h-6 w-6 mb-2" />
                    Add New Tour
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/bookings">
                    <Calendar className="h-6 w-6 mb-2" />
                    View Bookings
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/testimonials">
                    <MessageSquare className="h-6 w-6 mb-2" />
                    Review Testimonials
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-20 flex-col">
                  <Link href="/admin/blog/new">
                    <Eye className="h-6 w-6 mb-2" />
                    Write Blog Post
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pending Items Alert */}
        {(stats?.bookings.pending || 0) > 0 || (stats?.testimonials.pending || 0) > 0 ? (
          <Card className="border-amber-200 bg-amber-50">
            <CardHeader>
              <CardTitle className="flex items-center text-amber-800">
                <AlertCircle className="h-5 w-5 mr-2" />
                Items Requiring Attention
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {(stats?.bookings.pending || 0) > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-amber-700">
                      {stats?.bookings.pending} pending booking inquiries
                    </span>
                    <Button asChild size="sm" variant="outline">
                      <Link href="/admin/bookings?status=new">Review</Link>
                    </Button>
                  </div>
                )}
                {(stats?.testimonials.pending || 0) > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-amber-700">
                      {stats?.testimonials.pending} testimonials awaiting approval
                    </span>
                    <Button asChild size="sm" variant="outline">
                      <Link href="/admin/testimonials?approved=false">Review</Link>
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ) : null}
      </div>
    </AdminLayout>
  );
}
