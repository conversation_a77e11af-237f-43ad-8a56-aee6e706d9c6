import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Users, Award, Heart, Globe, Star, MapPin } from 'lucide-react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us - Garrisons Tours | Authentic Sri Lankan Travel Experiences',
  description: 'Learn about Garrisons Tours, Sri Lanka&apos;s trusted tour operator since 2015. Discover our story, values, and passionate team dedicated to creating authentic travel experiences.',
  keywords: ['about garrisons tours', 'sri lanka tour operator', 'travel company sri lanka', 'authentic tours', 'sustainable tourism'],
  openGraph: {
    title: 'About Garrisons Tours - Your Sri Lankan Travel Partner',
    description: 'Founded in 2015, Garrisons Tours is Sri Lanka&apos;s trusted partner for authentic travel experiences. Meet our team and discover our commitment to sustainable tourism.',
    type: 'website',
    url: 'https://garrisonstours.com/about',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Garrisons Tours - Authentic Sri Lankan Experiences',
    description: 'Learn about our story, values, and team dedicated to showcasing the best of Sri Lanka through sustainable tourism.',
  }
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-64 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
              About Garrisons Tours
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto px-4">
              Your trusted partner for authentic Sri Lankan experiences
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              Our Story
            </h2>
            <div className="w-24 h-1 bg-emerald-600 mx-auto mb-8"></div>
          </div>
          
          <div className="prose prose-lg mx-auto text-gray-600">
            <p className="text-xl leading-relaxed mb-6">
              Founded in 2015 by a group of passionate Sri Lankan travel enthusiasts, Garrisons Tours was born 
              from a simple belief: that every visitor to our beautiful island should experience its authentic 
              culture, breathtaking landscapes, and warm hospitality.
            </p>
            
            <p className="leading-relaxed mb-6">
              What started as a small family business has grown into one of Sri Lanka&apos;s most trusted tour 
              operators, but we&apos;ve never lost sight of our core values. We believe in sustainable tourism 
              that benefits local communities, preserves our natural heritage, and creates meaningful connections 
              between travelers and our homeland.
            </p>
            
            <p className="leading-relaxed">
              Today, we&apos;re proud to have welcomed thousands of guests from around the world, each taking 
              home not just memories, but a piece of Sri Lanka in their hearts. Our team of experienced guides, 
              drivers, and support staff are all locals who share our passion for showcasing the very best of 
              what Sri Lanka has to offer.
            </p>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              Our Values
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-emerald-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Authenticity</h3>
                <p className="text-gray-600">
                  We showcase the real Sri Lanka, from hidden gems to local traditions that make our island unique.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-amber-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-amber-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Community</h3>
                <p className="text-gray-600">
                  We support local communities and ensure tourism benefits the people who call Sri Lanka home.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Sustainability</h3>
                <p className="text-gray-600">
                  We practice responsible tourism that preserves our natural environment for future generations.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Excellence</h3>
                <p className="text-gray-600">
                  We strive for excellence in every aspect of your journey, from planning to execution.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The passionate people behind your unforgettable Sri Lankan experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: 'Rajesh Perera',
                role: 'Founder & CEO',
                description: 'With over 15 years in the tourism industry, Rajesh founded Garrisons Tours to share his love for Sri Lanka with the world.',
                expertise: 'Cultural Tours, Business Development'
              },
              {
                name: 'Samantha Silva',
                role: 'Head of Operations',
                description: 'Samantha ensures every tour runs smoothly, coordinating with our network of guides and partners across the island.',
                expertise: 'Operations Management, Quality Control'
              },
              {
                name: 'Nuwan Jayawardena',
                role: 'Senior Wildlife Guide',
                description: 'A naturalist with 12 years of experience, Nuwan is our go-to expert for wildlife safaris and nature tours.',
                expertise: 'Wildlife Photography, Conservation'
              }
            ].map((member, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="w-24 h-24 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-12 w-12 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-center mb-1">{member.name}</h3>
                  <p className="text-emerald-600 text-center font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm mb-3">{member.description}</p>
                  <div className="text-center">
                    <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                      {member.expertise}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-heading font-bold mb-4">
              Our Journey in Numbers
            </h2>
            <p className="text-xl text-emerald-100">
              The impact we&apos;ve made together
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">2,500+</div>
              <div className="text-emerald-100">Happy Travelers</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">150+</div>
              <div className="text-emerald-100">Tour Packages</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-emerald-100">Destinations</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-2">9</div>
              <div className="text-emerald-100">Years of Excellence</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-heading font-bold text-gray-900 mb-4">
              What Our Guests Say
            </h2>
            <p className="text-xl text-gray-600">
              Real experiences from real travelers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: 'Sarah Johnson',
                country: 'United Kingdom',
                rating: 5,
                text: 'Garrisons Tours made our Sri Lankan honeymoon absolutely magical. Every detail was perfect, and our guide became like family to us.'
              },
              {
                name: 'Michael Chen',
                country: 'Australia',
                rating: 5,
                text: 'The wildlife safari exceeded all expectations. Seeing leopards in Yala was a dream come true, thanks to our expert guide.'
              },
              {
                name: 'Emma Schmidt',
                country: 'Germany',
                rating: 5,
                text: 'The cultural tour was incredibly authentic. We learned so much about Sri Lankan history and traditions. Highly recommended!'
              }
            ].map((testimonial, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 italic">&quot;{testimonial.text}&quot;</p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                      <Users className="h-5 w-5 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-semibold">{testimonial.name}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {testimonial.country}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-heading font-bold text-gray-900 mb-4">
            Ready to Start Your Sri Lankan Adventure?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Let us create a personalized experience that will leave you with memories to last a lifetime.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-emerald-600 hover:bg-emerald-700 px-8 py-3 text-lg">
              <Link href="/tours">Browse Our Tours</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="px-8 py-3 text-lg">
              <Link href="/contact">Contact Us Today</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}