# Design Document

## Overview

The Garrisons Tours website will be built as a modern, server-side rendered Next.js application that showcases the authentic Sri Lankan travel experience. The design emphasizes visual storytelling through high-quality imagery, intuitive navigation, and seamless user interactions. The architecture supports both public-facing content and admin functionality while maintaining optimal performance and SEO.

The design philosophy centers on three core principles:
- **Authentic Sri Lankan Experience**: Warm, inviting design that reflects local culture and natural beauty
- **Professional Trust**: Clean, modern interface that builds confidence in the service quality
- **Effortless Discovery**: Intuitive user flows that guide visitors from inspiration to inquiry

## Architecture

### System Architecture

```mermaid
graph TB
    A[Next.js App Router] --> B[Public Pages]
    A --> C[Admin Dashboard]
    A --> D[API Routes]
    
    B --> E[Static Pages]
    B --> F[Dynamic Pages]
    
    E --> G[Home]
    E --> H[About]
    E --> I[Contact]
    E --> J[Gallery]
    
    F --> K[Tours Listing]
    F --> L[Tour Details]
    F --> M[Blog Listing]
    F --> N[Blog Post]
    
    C --> O[Tour Management]
    C --> P[Blog Management]
    C --> Q[Inquiry Management]
    
    D --> R[Tours API]
    D --> S[Bookings API]
    D --> T[Blog API]
    D --> U[Admin API]
    
    R --> V[MongoDB]
    S --> V
    T --> V
    U --> V
    
    V --> W[Tours Collection]
    V --> X[Bookings Collection]
    V --> Y[Blog Posts Collection]
    V --> Z[Users Collection]
```

### Technology Stack Integration

- **Frontend**: Next.js 14+ with App Router for optimal performance and SEO
- **Styling**: Tailwind CSS with custom design system for consistent branding
- **UI Components**: ShadCN UI for accessible, customizable components
- **Database**: MongoDB with Mongoose ODM for flexible document storage
- **Authentication**: NextAuth.js for secure admin access
- **Image Optimization**: Next.js Image component with Cloudinary integration
- **Form Handling**: React Hook Form with Zod validation

## Components and Interfaces

### Core Component Architecture

#### Layout Components
- **Header**: Navigation with logo, menu items, and contact CTA
- **Footer**: Contact info, social links, and secondary navigation
- **Sidebar**: Admin dashboard navigation and user controls

#### Content Components
- **TourCard**: Reusable tour package display with image, title, price, duration
- **TourGallery**: Image carousel with lightbox functionality
- **BookingForm**: Multi-step inquiry form with validation
- **TestimonialCard**: Customer review display with rating and photo
- **BlogCard**: Blog post preview with featured image and excerpt

#### Interactive Components
- **SearchFilter**: Tour filtering by category, duration, and price
- **DatePicker**: Custom date selection for booking inquiries
- **ImageUpload**: Admin interface for managing tour and blog images
- **RichTextEditor**: Content creation for blog posts and tour descriptions

### API Interface Design

#### Tours API (`/api/tours`)
```typescript
// GET /api/tours - Fetch all tours
interface ToursResponse {
  tours: Tour[];
  total: number;
  page: number;
}

// GET /api/tours/[slug] - Fetch single tour
interface TourResponse {
  tour: Tour;
  relatedTours: Tour[];
}

// POST /api/tours - Create tour (admin)
interface CreateTourRequest {
  title: string;
  slug: string;
  description: string;
  itinerary: ItineraryDay[];
  price: number;
  duration: number;
  images: string[];
  category: TourCategory;
}
```

#### Bookings API (`/api/bookings`)
```typescript
// POST /api/bookings - Submit inquiry
interface BookingInquiryRequest {
  tourId: string;
  customerName: string;
  email: string;
  phone: string;
  travelDates: {
    startDate: Date;
    endDate: Date;
  };
  groupSize: number;
  specialRequests?: string;
}

interface BookingInquiryResponse {
  success: boolean;
  inquiryId: string;
  message: string;
}
```

## Data Models

### Tour Package Schema
```typescript
interface Tour {
  _id: ObjectId;
  title: string;
  slug: string;
  description: string;
  shortDescription: string;
  itinerary: ItineraryDay[];
  price: {
    amount: number;
    currency: 'USD' | 'EUR';
    priceType: 'per_person' | 'per_group';
  };
  duration: {
    days: number;
    nights: number;
  };
  images: {
    featured: string;
    gallery: string[];
    alt: string[];
  };
  category: 'cultural' | 'wildlife' | 'adventure' | 'romantic' | 'family';
  highlights: string[];
  included: string[];
  excluded: string[];
  difficulty: 'easy' | 'moderate' | 'challenging';
  maxGroupSize: number;
  isActive: boolean;
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

interface ItineraryDay {
  day: number;
  title: string;
  description: string;
  activities: string[];
  accommodation?: string;
  meals: ('breakfast' | 'lunch' | 'dinner')[];
}
```

### Booking Inquiry Schema
```typescript
interface BookingInquiry {
  _id: ObjectId;
  tourId: ObjectId;
  tourTitle: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    country?: string;
  };
  travelDetails: {
    startDate: Date;
    endDate: Date;
    groupSize: number;
    adults: number;
    children: number;
  };
  specialRequests?: string;
  status: 'new' | 'contacted' | 'quoted' | 'confirmed' | 'cancelled';
  adminNotes?: string;
  submittedAt: Date;
  lastContactedAt?: Date;
}
```

### Blog Post Schema
```typescript
interface BlogPost {
  _id: ObjectId;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  author: {
    name: string;
    avatar?: string;
  };
  category: string[];
  tags: string[];
  relatedTours: ObjectId[];
  isPublished: boolean;
  publishedAt?: Date;
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}
```

## Error Handling

### Client-Side Error Handling
- **Form Validation**: Real-time validation with clear error messages
- **Network Errors**: Retry mechanisms with user-friendly error states
- **Image Loading**: Graceful fallbacks for failed image loads
- **Route Errors**: Custom 404 and 500 pages with navigation options

### Server-Side Error Handling
- **API Errors**: Structured error responses with appropriate HTTP status codes
- **Database Errors**: Connection retry logic and graceful degradation
- **Validation Errors**: Detailed field-level error reporting
- **Authentication Errors**: Secure error messages without information leakage

### Error Monitoring
- **Logging**: Structured logging for debugging and monitoring
- **Alerting**: Critical error notifications for admin users
- **User Feedback**: Error reporting mechanism for user-reported issues

## Testing Strategy

### Unit Testing
- **Components**: React Testing Library for component behavior
- **API Routes**: Jest for API endpoint testing
- **Utilities**: Pure function testing for business logic
- **Database Models**: Mongoose model validation testing

### Integration Testing
- **User Flows**: End-to-end testing for critical user journeys
- **API Integration**: Full request/response cycle testing
- **Database Operations**: CRUD operation testing with test database

### Performance Testing
- **Core Web Vitals**: Lighthouse CI for performance monitoring
- **Load Testing**: API endpoint performance under load
- **Image Optimization**: Automated testing for image delivery

### Accessibility Testing
- **WCAG Compliance**: Automated accessibility testing
- **Screen Reader**: Manual testing with assistive technologies
- **Keyboard Navigation**: Full keyboard accessibility verification

## Design System Specifications

### Color Palette
```css
:root {
  /* Primary Colors - Inspired by Sri Lankan landscapes */
  --primary-emerald: #10b981; /* Lush tea plantations */
  --primary-emerald-dark: #059669;
  --primary-emerald-light: #34d399;
  
  /* Secondary Colors - Cultural elements */
  --secondary-amber: #f59e0b; /* Temple gold */
  --secondary-amber-dark: #d97706;
  --secondary-amber-light: #fbbf24;
  
  /* Accent Colors - Natural beauty */
  --accent-blue: #0ea5e9; /* Ocean and sky */
  --accent-coral: #f97316; /* Sunset hues */
  
  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-800: #262626;
  --neutral-900: #171717;
}
```

### Typography System
```css
/* Primary Font - Headings */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Secondary Font - Body Text */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

.font-heading {
  font-family: 'Playfair Display', serif;
}

.font-body {
  font-family: 'Inter', sans-serif;
}
```

### Layout Principles
- **Grid System**: 12-column responsive grid with consistent gutters
- **Spacing Scale**: 8px base unit for consistent spacing (4, 8, 16, 24, 32, 48, 64px)
- **Container Widths**: Max-width containers for optimal reading experience
- **Breakpoints**: Mobile-first responsive design (sm: 640px, md: 768px, lg: 1024px, xl: 1280px)

### Visual Hierarchy
- **Hero Sections**: Full-width imagery with overlay text
- **Content Sections**: Alternating layouts with ample white space
- **Card Layouts**: Consistent card design for tours, blogs, and testimonials
- **Call-to-Action**: Prominent buttons with hover states and accessibility focus

This design provides a solid foundation for building an elegant, performant, and user-friendly website that authentically represents the Garrisons Tours brand while meeting all technical requirements.