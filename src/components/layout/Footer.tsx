import Link from 'next/link';
// import Image from 'next/image';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <span className="text-2xl font-heading font-bold text-white">
              Garrisons Tours
            </span>
            <p className="text-gray-300 text-sm leading-relaxed">
              Discover the authentic beauty of Sri Lanka with our personalized tour experiences. 
              From ancient temples to pristine beaches, we create unforgettable memories.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/tours" className="text-gray-300 hover:text-white transition-colors">
                  Our Tours
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-white transition-colors">
                  Travel Blog
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-gray-300 hover:text-white transition-colors">
                  Photo Gallery
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Tour Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Tour Categories</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/tours?category=cultural" className="text-gray-300 hover:text-white transition-colors">
                  Cultural Tours
                </Link>
              </li>
              <li>
                <Link href="/tours?category=wildlife" className="text-gray-300 hover:text-white transition-colors">
                  Wildlife Safari
                </Link>
              </li>
              <li>
                <Link href="/tours?category=adventure" className="text-gray-300 hover:text-white transition-colors">
                  Adventure Tours
                </Link>
              </li>
              <li>
                <Link href="/tours?category=romantic" className="text-gray-300 hover:text-white transition-colors">
                  Romantic Getaways
                </Link>
              </li>
              <li>
                <Link href="/tours?category=family" className="text-gray-300 hover:text-white transition-colors">
                  Family Tours
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-emerald-400 mt-0.5 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>123 Galle Road</p>
                  <p>Colombo 03, Sri Lanka</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-emerald-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm">+94 77 123 4567</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-emerald-400 flex-shrink-0" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-400">
                <strong>Business Hours:</strong><br />
                Mon - Sat: 8:00 AM - 6:00 PM<br />
                Sunday: 9:00 AM - 4:00 PM
              </p>
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Garrisons Tours. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;