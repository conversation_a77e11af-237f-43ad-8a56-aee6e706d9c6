'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Users, 
  MapPin, 
  Star, 
  Calendar,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Heart,
  Share2
} from 'lucide-react';
import { Loading } from '@/components/ui/loading';
import BookingForm from '@/components/BookingForm';

interface Tour {
  _id: string;
  title: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: {
    amount: number;
    currency: string;
    priceType: string;
  };
  duration: {
    days: number;
    nights: number;
  };
  images: {
    featured: string;
    gallery: string[];
    alt: string[];
  };
  category: string;
  highlights: string[];
  included: string[];
  excluded: string[];
  difficulty: string;
  maxGroupSize: number;
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    activities: string[];
    accommodation?: string;
    meals: string[];
  }>;
  seoMeta: {
    title: string;
    description: string;
    keywords: string[];
  };
}

interface RelatedTour {
  _id: string;
  title: string;
  slug: string;
  shortDescription: string;
  price: {
    amount: number;
    currency: string;
  };
  duration: {
    days: number;
    nights: number;
  };
  images: {
    featured: string;
  };
}

export default function TourDetailPage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [tour, setTour] = useState<Tour | null>(null);
  const [relatedTours, setRelatedTours] = useState<RelatedTour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (slug) {
      fetchTour();
    }
  }, [slug]);

  const fetchTour = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/tours/${slug}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Tour not found');
        } else {
          setError('Failed to load tour details');
        }
        return;
      }

      const data = await response.json();
      setTour(data.tour);
      setRelatedTours(data.relatedTours || []);
      setError(null);
    } catch (err) {
      setError('Failed to load tour details');
      console.error('Error fetching tour:', err);
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'cultural': return 'bg-amber-100 text-amber-800';
      case 'wildlife': return 'bg-green-100 text-green-800';
      case 'adventure': return 'bg-blue-100 text-blue-800';
      case 'romantic': return 'bg-pink-100 text-pink-800';
      case 'family': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Loading text="Loading tour details..." className="py-20" />
      </div>
    );
  }

  if (error || !tour) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Tour not found'}
          </h1>
          <p className="text-gray-600 mb-6">
            The tour you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link href="/tours">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tours
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-96 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white max-w-4xl mx-auto px-4">
            <div className="mb-4">
              <Badge className={`${getCategoryColor(tour.category)} mb-2`}>
                {tour.category.charAt(0).toUpperCase() + tour.category.slice(1)}
              </Badge>
              <Badge className={getDifficultyColor(tour.difficulty)}>
                {tour.difficulty.charAt(0).toUpperCase() + tour.difficulty.slice(1)}
              </Badge>
            </div>
            <h1 className="text-4xl md:text-6xl font-heading font-bold mb-4">
              {tour.title}
            </h1>
            <p className="text-xl text-emerald-100 mb-6">
              {tour.shortDescription}
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-emerald-100">
              <div className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                <span>{tour.duration.days} Days, {tour.duration.nights} Nights</span>
              </div>
              <div className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                <span>Max {tour.maxGroupSize} People</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                <span>Sri Lanka</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Navigation Tabs */}
            <div className="flex flex-wrap gap-2 mb-8 border-b">
              {[
                { id: 'overview', label: 'Overview' },
                { id: 'itinerary', label: 'Itinerary' },
                { id: 'included', label: 'What\'s Included' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-emerald-600 text-emerald-600'
                      : 'border-transparent text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-2xl font-heading font-bold mb-4">About This Tour</h2>
                  <p className="text-gray-600 leading-relaxed">{tour.description}</p>
                </div>

                {tour.highlights && tour.highlights.length > 0 && (
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Tour Highlights</h3>
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {tour.highlights.map((highlight, index) => (
                        <li key={index} className="flex items-start">
                          <Star className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-600">{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'itinerary' && (
              <div>
                <h2 className="text-2xl font-heading font-bold mb-6">Day by Day Itinerary</h2>
                <div className="space-y-6">
                  {tour.itinerary && tour.itinerary.map((day, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <span className="bg-emerald-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                            {day.day}
                          </span>
                          {day.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 mb-4">{day.description}</p>
                        {day.activities && day.activities.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-semibold mb-2">Activities:</h4>
                            <ul className="list-disc list-inside text-gray-600 space-y-1">
                              {day.activities.map((activity, actIndex) => (
                                <li key={actIndex}>{activity}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                          {day.accommodation && (
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span>{day.accommodation}</span>
                            </div>
                          )}
                          {day.meals && day.meals.length > 0 && (
                            <div className="flex items-center">
                              <span className="mr-1">🍽️</span>
                              <span>{day.meals.join(', ')}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'included' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-green-600">What's Included</h3>
                  <ul className="space-y-2">
                    {tour.included && tour.included.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-red-600">What's Not Included</h3>
                  <ul className="space-y-2">
                    {tour.excluded && tour.excluded.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <XCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* Pricing Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-center">
                    <div className="text-3xl font-bold text-emerald-600">
                      ${tour.price.amount}
                    </div>
                    <div className="text-sm text-gray-500">
                      {tour.price.priceType.replace('_', ' ')}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">Duration</span>
                    <span className="font-semibold">{tour.duration.days} Days</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">Group Size</span>
                    <span className="font-semibold">Max {tour.maxGroupSize}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">Difficulty</span>
                    <Badge className={getDifficultyColor(tour.difficulty)}>
                      {tour.difficulty}
                    </Badge>
                  </div>
                  
                  <div className="space-y-3 pt-4">
                    <Button asChild className="w-full bg-emerald-600 hover:bg-emerald-700" size="lg">
                      <Link href={`/tours/${tour.slug}#booking`}>
                        <Calendar className="h-4 w-4 mr-2" />
                        Book This Tour
                      </Link>
                    </Button>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Heart className="h-4 w-4 mr-1" />
                        Save
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Need Help?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    Have questions about this tour? Our travel experts are here to help.
                  </p>
                  <div className="space-y-2">
                    <Button asChild variant="outline" className="w-full">
                      <Link href="/contact">Contact Us</Link>
                    </Button>
                    <div className="text-center text-sm text-gray-500">
                      Call: +94 77 123 4567
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Booking Form */}
        <section className="mt-16">
          <BookingForm
            tourId={tour._id}
            tourTitle={tour.title}
            tourPrice={tour.price.amount}
            className="max-w-4xl mx-auto"
          />
        </section>

        {/* Related Tours */}
        {relatedTours.length > 0 && (
          <section className="mt-16">
            <h2 className="text-3xl font-heading font-bold text-center mb-8">
              You Might Also Like
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedTours.map((relatedTour) => (
                <Card key={relatedTour._id} className="hover:shadow-lg transition-shadow">
                  <div className="h-48 bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="text-4xl mb-2">🌴</div>
                      <p className="font-medium">Sri Lanka</p>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-2">{relatedTour.title}</h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {relatedTour.shortDescription}
                    </p>
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-emerald-600 font-bold">
                        ${relatedTour.price.amount}
                      </span>
                      <span className="text-gray-500 text-sm">
                        {relatedTour.duration.days} Days
                      </span>
                    </div>
                    <Button asChild variant="outline" size="sm" className="w-full">
                      <Link href={`/tours/${relatedTour.slug}`}>View Details</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
