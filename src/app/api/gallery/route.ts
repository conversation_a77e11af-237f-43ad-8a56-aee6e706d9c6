import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import GalleryImage from '@/models/Gallery';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const tourId = searchParams.get('tourId');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, any> = { isActive: true };
    if (category && category !== 'all') {
      query.category = category;
    }
    if (tourId) {
      query.tourId = tourId;
    }

    // Get gallery images with pagination
    const images = await GalleryImage.find(query)
      .sort({ sortOrder: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('tourId', 'title slug')
      .populate('uploadedBy', 'name')
      .lean();

    // Get total count for pagination
    const total = await GalleryImage.countDocuments(query);

    // Get categories for filtering
    const categories = await GalleryImage.distinct('category', { isActive: true });

    return NextResponse.json({
      images,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: skip + images.length < total,
      categories
    });

  } catch (error) {
    console.error('Error fetching gallery images:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gallery images' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    
    // Validate required fields
    const { title, imageUrl, category, uploadedBy } = body;
    
    if (!title || !imageUrl || !category || !uploadedBy) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create new gallery image
    const galleryImage = new GalleryImage(body);
    await galleryImage.save();

    return NextResponse.json(galleryImage, { status: 201 });

  } catch (error) {
    console.error('Error creating gallery image:', error);
    return NextResponse.json(
      { error: 'Failed to create gallery image' },
      { status: 500 }
    );
  }
}
