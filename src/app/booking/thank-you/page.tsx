'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  CheckCircle, 
  Calendar, 
  Mail, 
  Phone, 
  Clock,
  ArrowLeft,
  Download,
  Share2
} from 'lucide-react';

function ThankYouContent() {
  const searchParams = useSearchParams();
  const bookingRef = searchParams.get('ref');
  const [currentTime] = useState(new Date());

  useEffect(() => {
    // Track successful booking conversion
    if (typeof window !== 'undefined' && bookingRef) {
      // You can add analytics tracking here
      console.log('Booking inquiry submitted:', bookingRef);
    }
  }, [bookingRef]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-64 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <div className="mb-4">
              <CheckCircle className="h-16 w-16 mx-auto text-emerald-200" />
            </div>
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
              Thank You!
            </h1>
            <p className="text-xl text-emerald-100 max-w-2xl mx-auto px-4">
              Your booking inquiry has been successfully submitted
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Booking Confirmation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-emerald-600" />
                Booking Confirmation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {bookingRef && (
                <div className="bg-emerald-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-emerald-800">Reference Number:</span>
                    <span className="font-bold text-emerald-600 text-lg">
                      #{bookingRef.slice(-8).toUpperCase()}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Inquiry Received</p>
                    <p className="text-sm text-gray-600">
                      We've received your booking inquiry and will review it shortly.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Confirmation Email Sent</p>
                    <p className="text-sm text-gray-600">
                      Check your email for a detailed confirmation of your inquiry.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Response Within 24 Hours</p>
                    <p className="text-sm text-gray-600">
                      Our travel experts will contact you with a personalized quote and itinerary.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <p className="text-sm text-gray-500">
                  Submitted on: {currentTime.toLocaleDateString()} at {currentTime.toLocaleTimeString()}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>What Happens Next?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-emerald-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    1
                  </div>
                  <div>
                    <p className="font-medium">Review & Planning</p>
                    <p className="text-sm text-gray-600">
                      Our team will review your requirements and create a customized itinerary.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="bg-emerald-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    2
                  </div>
                  <div>
                    <p className="font-medium">Personalized Quote</p>
                    <p className="text-sm text-gray-600">
                      You'll receive a detailed quote with accommodation options and pricing.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="bg-emerald-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    3
                  </div>
                  <div>
                    <p className="font-medium">Booking Confirmation</p>
                    <p className="text-sm text-gray-600">
                      Once you approve, we'll handle all bookings and send you a complete travel package.
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-800 font-medium mb-2">
                    Need immediate assistance?
                  </p>
                  <div className="space-y-1 text-sm text-blue-700">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <span>+94 77 123 4567</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span><EMAIL></span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="mt-12 text-center space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-emerald-600 hover:bg-emerald-700">
              <Link href="/tours">
                Browse More Tours
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg">
              <Link href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Link>
            </Button>
          </div>

          {bookingRef && (
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Download Confirmation
              </Button>
              
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share with Friends
              </Button>
            </div>
          )}
        </div>

        {/* Additional Information */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Travel Tips</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                While you wait for our response, check out our travel tips and guides 
                to help you prepare for your Sri Lankan adventure.
              </p>
              <Button asChild variant="link" className="p-0 mt-2">
                <Link href="/blog">Read Travel Tips</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Photo Gallery</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Get inspired by browsing our photo gallery showcasing the beauty 
                and culture of Sri Lanka.
              </p>
              <Button asChild variant="link" className="p-0 mt-2">
                <Link href="/gallery">View Gallery</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Read what other travelers have to say about their experiences 
                with Garrisons Tours.
              </p>
              <Button asChild variant="link" className="p-0 mt-2">
                <Link href="/testimonials">Read Reviews</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function ThankYouPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
      <ThankYouContent />
    </Suspense>
  );
}
