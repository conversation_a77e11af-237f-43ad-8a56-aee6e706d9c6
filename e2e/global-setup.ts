import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('✅ Development server is ready');

    // Perform any global setup tasks here
    // For example, seed test data, authenticate admin user, etc.

    // Check if the app is running correctly
    const title = await page.title();
    if (!title.includes('Garrisons Tours')) {
      throw new Error('Application not loaded correctly');
    }

    console.log('✅ Application is running correctly');

    // You can add more setup tasks here:
    // - Create test database
    // - Seed test data
    // - Set up authentication tokens
    // - Clear cache
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ Global setup completed');
}

export default globalSetup;
