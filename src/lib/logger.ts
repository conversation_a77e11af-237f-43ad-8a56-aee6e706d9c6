import { NextRequest } from 'next/server';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

// Log entry interface
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  error?: Error;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  url?: string;
  method?: string;
}

// Logger configuration
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  maxLogSize: number;
  rotateDaily: boolean;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private readonly maxBufferSize = 100;

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG,
      enableConsole: true,
      enableFile: process.env.NODE_ENV === 'production',
      enableRemote: process.env.NODE_ENV === 'production',
      remoteEndpoint: process.env.LOG_ENDPOINT,
      maxLogSize: 10 * 1024 * 1024, // 10MB
      rotateDaily: true,
      ...config,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
    };
  }

  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    const context = entry.context ? ` | Context: ${JSON.stringify(entry.context)}` : '';
    const error = entry.error ? ` | Error: ${entry.error.message}\n${entry.error.stack}` : '';
    
    return `[${timestamp}] ${levelName}: ${entry.message}${context}${error}`;
  }

  private async writeLog(entry: LogEntry): Promise<void> {
    // Console logging
    if (this.config.enableConsole) {
      const formatted = this.formatLogEntry(entry);
      
      switch (entry.level) {
        case LogLevel.DEBUG:
          console.debug(formatted);
          break;
        case LogLevel.INFO:
          console.info(formatted);
          break;
        case LogLevel.WARN:
          console.warn(formatted);
          break;
        case LogLevel.ERROR:
        case LogLevel.FATAL:
          console.error(formatted);
          break;
      }
    }

    // Buffer for batch processing
    this.logBuffer.push(entry);
    
    if (this.logBuffer.length >= this.maxBufferSize) {
      await this.flushLogs();
    }

    // Remote logging for critical errors
    if (entry.level >= LogLevel.ERROR && this.config.enableRemote) {
      await this.sendToRemote(entry);
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logs = [...this.logBuffer];
    this.logBuffer = [];

    // File logging
    if (this.config.enableFile && typeof window === 'undefined') {
      try {
        const fs = await import('fs/promises');
        const path = await import('path');
        
        const logDir = path.join(process.cwd(), 'logs');
        const logFile = path.join(logDir, `app-${new Date().toISOString().split('T')[0]}.log`);
        
        // Ensure log directory exists
        try {
          await fs.mkdir(logDir, { recursive: true });
        } catch (error) {
          // Directory might already exist
        }

        const logContent = logs.map(entry => this.formatLogEntry(entry)).join('\n') + '\n';
        await fs.appendFile(logFile, logContent);
      } catch (error) {
        console.error('Failed to write logs to file:', error);
      }
    }

    // Remote logging
    if (this.config.enableRemote) {
      await this.sendBatchToRemote(logs);
    }
  }

  private async sendToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.remoteEndpoint) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      console.error('Failed to send log to remote endpoint:', error);
    }
  }

  private async sendBatchToRemote(entries: LogEntry[]): Promise<void> {
    if (!this.config.remoteEndpoint || entries.length === 0) return;

    try {
      await fetch(`${this.config.remoteEndpoint}/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ logs: entries }),
      });
    } catch (error) {
      console.error('Failed to send batch logs to remote endpoint:', error);
    }
  }

  // Public logging methods
  debug(message: string, context?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
    this.writeLog(entry);
  }

  info(message: string, context?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    this.writeLog(entry);
  }

  warn(message: string, context?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    const entry = this.createLogEntry(LogLevel.WARN, message, context);
    this.writeLog(entry);
  }

  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
    this.writeLog(entry);
  }

  fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.FATAL)) return;
    const entry = this.createLogEntry(LogLevel.FATAL, message, context, error);
    this.writeLog(entry);
  }

  // Request logging
  logRequest(req: NextRequest, context?: Record<string, unknown>): void {
    const entry = this.createLogEntry(
      LogLevel.INFO,
      `${req.method} ${req.url}`,
      {
        method: req.method,
        url: req.url,
        ip: req.ip || req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip'),
        userAgent: req.headers.get('user-agent'),
        ...context,
      }
    );
    this.writeLog(entry);
  }

  // Security logging
  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    context?: Record<string, unknown>
  ): void {
    const level = severity === 'critical' ? LogLevel.FATAL : 
                  severity === 'high' ? LogLevel.ERROR :
                  severity === 'medium' ? LogLevel.WARN : LogLevel.INFO;

    const entry = this.createLogEntry(
      level,
      `Security Event: ${event}`,
      { severity, ...context }
    );
    this.writeLog(entry);
  }

  // Performance logging
  logPerformance(
    operation: string,
    duration: number,
    context?: Record<string, unknown>
  ): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.INFO;
    const entry = this.createLogEntry(
      level,
      `Performance: ${operation} took ${duration}ms`,
      { operation, duration, ...context }
    );
    this.writeLog(entry);
  }

  // Business logic logging
  logBusinessEvent(
    event: string,
    context?: Record<string, unknown>
  ): void {
    const entry = this.createLogEntry(
      LogLevel.INFO,
      `Business Event: ${event}`,
      context
    );
    this.writeLog(entry);
  }

  // Flush remaining logs (call on app shutdown)
  async shutdown(): Promise<void> {
    await this.flushLogs();
  }
}

// Create singleton logger instance
const logger = new Logger();

// Graceful shutdown handling
if (typeof process !== 'undefined') {
  process.on('SIGINT', async () => {
    await logger.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    await logger.shutdown();
    process.exit(0);
  });
}

export default logger;

// Convenience exports
export const log = logger;
export { Logger, LogLevel };

// Request logging middleware helper
export function createRequestLogger(req: NextRequest) {
  const requestId = crypto.randomUUID();
  
  return {
    requestId,
    log: (message: string, context?: Record<string, unknown>) => {
      logger.info(message, {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip || req.headers.get('x-forwarded-for'),
        ...context,
      });
    },
    error: (message: string, error?: Error, context?: Record<string, unknown>) => {
      logger.error(message, error, {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip || req.headers.get('x-forwarded-for'),
        ...context,
      });
    },
  };
}
