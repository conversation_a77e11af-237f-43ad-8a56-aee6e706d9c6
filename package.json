{"name": "garrisons-tours", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "seed": "tsx src/scripts/seed-data.ts", "migrate": "tsx src/scripts/migrate-content.ts", "backup": "curl -X GET http://localhost:3000/api/admin/backup", "postinstall": "playwright install", "clean": "rm -rf .next out dist", "prepare": "husky install || true"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.5", "@radix-ui/react-tabs": "^1.1.5", "@radix-ui/react-toast": "^1.2.5", "@tailwindcss/typography": "^0.5.16", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "express-rate-limit": "^8.0.1", "express-slow-down": "^3.0.0", "lodash.castarray": "^4.4.0", "lodash.merge": "^4.6.2", "lucide-react": "^0.537.0", "mongodb": "^5.9.2", "mongoose": "^8.17.1", "next": "15.4.6", "next-auth": "^4.24.11", "nodemailer": "^6.9.17", "react": "19.1.0", "react-day-picker": "^9.4.3", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.1.0", "zod": "^4.0.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.48.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}