'use client';

import { useState, useMemo, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, X, ZoomIn, ChevronLeft, ChevronRight } from 'lucide-react';
import { getPublicImages, filterImages, type GalleryImage } from '@/lib/gallery-utils';

const ITEMS_PER_PAGE = 12;

const categoryLabels: Record<string, string> = {
  'destinations': 'Destinations',
  'activities': 'Activities',
  'testimonials': 'Customer Photos',
  'tours': 'Tour Highlights',
  'culture': 'Culture & Heritage',
  'wildlife': 'Wildlife'
};

/**
 * Gallery page component that displays images from the public folder
 * with search, filtering, pagination, and lightbox functionality
 */
export default function GalleryPage() {
  // Get all images from public folder
  const allImages = useMemo(() => getPublicImages(), []);
  const categories = useMemo(() => ['all', ...new Set(allImages.map(img => img.category))], [allImages]);
  
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Filter and paginate images
  const filteredImages = useMemo(() => {
    return filterImages(allImages, searchTerm, selectedCategory);
  }, [allImages, searchTerm, selectedCategory]);

  const totalPages = Math.ceil(filteredImages.length / ITEMS_PER_PAGE);
  const paginatedImages = useMemo(() => {
    const start = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredImages.slice(start, start + ITEMS_PER_PAGE);
  }, [filteredImages, currentPage]);

  // Reset to first page when search/filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCategory]);

  /**
   * Navigate between images in lightbox
   */
  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentImageIndex(prev => prev === 0 ? filteredImages.length - 1 : prev - 1);
    } else {
      setCurrentImageIndex(prev => prev === filteredImages.length - 1 ? 0 : prev + 1);
    }
  };

  // Keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;
      if (e.key === 'Escape') setLightboxOpen(false);
      if (e.key === 'ArrowLeft') navigateImage('prev');
      if (e.key === 'ArrowRight') navigateImage('next');
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [lightboxOpen, currentImageIndex, filteredImages.length]);

  /**
   * Handle image click to open lightbox
   */
  const handleImageClick = (imageUrl: string) => {
    const index = filteredImages.findIndex(img => img.imageUrl === imageUrl);
    if (index !== -1) {
      setCurrentImageIndex(index);
      setLightboxOpen(true);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Photo Gallery</h1>
      
      {/* Search and Filter Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search photos..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories
              .filter(cat => cat !== 'all')
              .map(category => (
                <SelectItem key={category} value={category}>
                  {categoryLabels[category] || category}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      {/* Image Grid */}
      {paginatedImages.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {paginatedImages.map((image) => (
            <Card key={image._id} className="overflow-hidden group relative hover:shadow-lg transition-shadow">
              <div className="aspect-[4/3] overflow-hidden">
                <img
                  src={image.imageUrl}
                  alt={image.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105 cursor-pointer"
                  onClick={() => handleImageClick(image.imageUrl)}
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="bg-white/80 hover:bg-white text-gray-800"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleImageClick(image.imageUrl);
                    }}
                  >
                    <ZoomIn className="h-5 w-5" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium text-lg mb-1 line-clamp-1">{image.title}</h3>
                {image.description && (
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">{image.description}</p>
                )}
                <div className="flex flex-wrap gap-1 mt-2">
                  <Badge variant="secondary" className="text-xs capitalize">
                    {image.category}
                  </Badge>
                  {image.tags.slice(0, 2).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {image.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{image.tags.length - 2}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">No images found matching your search.</p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8 gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4 mr-1" /> Previous
          </Button>
          <div className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </div>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
          >
            Next <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}

      {/* Lightbox Modal */}
      {lightboxOpen && filteredImages.length > 0 && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setLightboxOpen(false)}
        >
          <button 
            onClick={(e) => {
              e.stopPropagation();
              setLightboxOpen(false);
            }}
            className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
          >
            <X className="h-8 w-8" />
          </button>
          
          <div className="relative w-full max-w-5xl max-h-[90vh] flex flex-col">
            <div className="relative flex-grow flex items-center justify-center">
              <img 
                src={filteredImages[currentImageIndex].imageUrl} 
                alt={filteredImages[currentImageIndex].title}
                className="max-h-[70vh] w-auto max-w-full object-contain"
                draggable={false}
              />
              
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('prev');
                }}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all"
              >
                <ChevronLeft className="h-6 w-6" />
              </button>
              
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('next');
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-all"
              >
                <ChevronRight className="h-6 w-6" />
              </button>
            </div>
            
            <div className="mt-4 text-white text-center bg-black/50 p-4 rounded-lg">
              <h3 className="text-xl font-semibold">{filteredImages[currentImageIndex].title}</h3>
              {filteredImages[currentImageIndex].description && (
                <p className="text-gray-300 mt-2">{filteredImages[currentImageIndex].description}</p>
              )}
              <div className="flex justify-center gap-2 mt-3 flex-wrap">
                {filteredImages[currentImageIndex].tags.map((tag, i) => (
                  <Badge key={i} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
              <div className="mt-3 text-sm text-gray-400">
                {currentImageIndex + 1} of {filteredImages.length}
              </div>
            </div>
            
            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
              {filteredImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex(idx);
                  }}
                  className={`h-2 w-2 rounded-full transition-all ${currentImageIndex === idx ? 'bg-white w-6' : 'bg-white/50'}`}
                  aria-label={`Go to image ${idx + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
