import mongoose, { Document, Schema } from 'mongoose';

export interface IBookingInquiry extends Document {
  tourId: mongoose.Types.ObjectId;
  tourTitle: string;
  customer: {
    name: string;
    email: string;
    phone: string;
    country?: string;
  };
  travelDetails: {
    startDate: Date;
    endDate: Date;
    groupSize: number;
    adults: number;
    children: number;
  };
  specialRequests?: string;
  status: 'new' | 'contacted' | 'quoted' | 'confirmed' | 'cancelled';
  adminNotes?: string;
  submittedAt: Date;
  lastContactedAt?: Date;
}

const BookingInquirySchema = new Schema<IBookingInquiry>({
  tourId: { type: Schema.Types.ObjectId, ref: 'Tour', required: true },
  tourTitle: { type: String, required: true },
  customer: {
    name: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, required: true },
    country: { type: String }
  },
  travelDetails: {
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    groupSize: { type: Number, required: true, min: 1 },
    adults: { type: Number, required: true, min: 1 },
    children: { type: Number, default: 0, min: 0 }
  },
  specialRequests: { type: String },
  status: {
    type: String,
    enum: ['new', 'contacted', 'quoted', 'confirmed', 'cancelled'],
    default: 'new'
  },
  adminNotes: { type: String },
  submittedAt: { type: Date, default: Date.now },
  lastContactedAt: { type: Date }
});

// Create indexes for better performance
BookingInquirySchema.index({ tourId: 1 });
BookingInquirySchema.index({ status: 1 });
BookingInquirySchema.index({ submittedAt: -1 });
BookingInquirySchema.index({ 'customer.email': 1 });

export default mongoose.models.BookingInquiry || mongoose.model<IBookingInquiry>('BookingInquiry', BookingInquirySchema);