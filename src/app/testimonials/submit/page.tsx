'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { 
  Star,
  ArrowLeft,
  Send,
  Heart
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';

interface Tour {
  _id: string;
  title: string;
  slug: string;
}

interface FormData {
  customerName: string;
  customerEmail: string;
  customerCountry: string;
  tourId?: string;
  tourTitle?: string;
  rating: number;
  title: string;
  content: string;
  travelDate: Date | undefined;
}

export default function SubmitTestimonialPage() {
  const router = useRouter();
  const [tours, setTours] = useState<Tour[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const [formData, setFormData] = useState<FormData>({
    customerName: '',
    customerEmail: '',
    customerCountry: '',
    tourId: '',
    tourTitle: '',
    rating: 5,
    title: '',
    content: '',
    travelDate: undefined
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
    'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland',
    'Austria', 'Belgium', 'Italy', 'Spain', 'Japan', 'South Korea', 'Singapore',
    'New Zealand', 'Ireland', 'India', 'China', 'Brazil', 'Argentina', 'Other'
  ];

  useEffect(() => {
    fetchTours();
  }, []);

  const fetchTours = async () => {
    try {
      const response = await fetch('/api/tours?limit=100');
      if (response.ok) {
        const data = await response.json();
        setTours(data.tours || []);
      }
    } catch (error) {
      console.error('Error fetching tours:', error);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) newErrors.customerName = 'Name is required';
    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }
    if (!formData.customerCountry) newErrors.customerCountry = 'Country is required';
    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.content.trim()) newErrors.content = 'Review content is required';
    if (!formData.travelDate) newErrors.travelDate = 'Travel date is required';
    if (formData.rating < 1 || formData.rating > 5) newErrors.rating = 'Please select a rating';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch('/api/testimonials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          tourTitle: formData.tourId ? tours.find(t => t._id === formData.tourId)?.title : formData.tourTitle
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        // Redirect to thank you page after a short delay
        setTimeout(() => {
          router.push('/testimonials?submitted=true');
        }, 2000);
      } else {
        setSubmitStatus('error');
        setErrorMessage(data.error || 'Failed to submit testimonial');
      }
    } catch (error) {
      console.error('Error submitting testimonial:', error);
      setSubmitStatus('error');
      setErrorMessage('Failed to submit testimonial. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const renderStars = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="flex gap-1">
        {Array.from({ length: 5 }, (_, i) => (
          <button
            key={i}
            type="button"
            onClick={() => onRatingChange(i + 1)}
            className="focus:outline-none"
          >
            <Star
              className={`h-8 w-8 transition-colors ${
                i < rating 
                  ? 'text-yellow-400 fill-current hover:text-yellow-500' 
                  : 'text-gray-300 hover:text-yellow-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  if (submitStatus === 'success') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Thank You!
              </h2>
              <p className="text-gray-600 mb-6">
                Your testimonial has been submitted successfully. We'll review it and publish it soon.
              </p>
              <Button asChild className="bg-emerald-600 hover:bg-emerald-700">
                <Link href="/testimonials">
                  View All Testimonials
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative h-48 bg-gradient-to-r from-emerald-600 to-emerald-800">
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-3xl md:text-4xl font-heading font-bold mb-4">
              Share Your Experience
            </h1>
            <p className="text-lg text-emerald-100 max-w-2xl mx-auto px-4">
              Tell us about your Sri Lankan adventure
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back Button */}
        <div className="mb-6">
          <Button asChild variant="outline">
            <Link href="/testimonials">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Testimonials
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Write Your Testimonial</CardTitle>
            <p className="text-gray-600">
              Share your experience to help other travelers discover the beauty of Sri Lanka
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customerName">Full Name *</Label>
                    <Input
                      id="customerName"
                      value={formData.customerName}
                      onChange={(e) => updateFormData('customerName', e.target.value)}
                      placeholder="Your full name"
                      className={errors.customerName ? 'border-red-500' : ''}
                    />
                    {errors.customerName && <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>}
                  </div>
                  
                  <div>
                    <Label htmlFor="customerEmail">Email Address *</Label>
                    <Input
                      id="customerEmail"
                      type="email"
                      value={formData.customerEmail}
                      onChange={(e) => updateFormData('customerEmail', e.target.value)}
                      placeholder="<EMAIL>"
                      className={errors.customerEmail ? 'border-red-500' : ''}
                    />
                    {errors.customerEmail && <p className="text-red-500 text-sm mt-1">{errors.customerEmail}</p>}
                  </div>
                </div>

                <div>
                  <Label htmlFor="customerCountry">Country *</Label>
                  <Select 
                    value={formData.customerCountry} 
                    onValueChange={(value) => updateFormData('customerCountry', value)}
                  >
                    <SelectTrigger className={errors.customerCountry ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select your country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country} value={country}>
                          {country}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.customerCountry && <p className="text-red-500 text-sm mt-1">{errors.customerCountry}</p>}
                </div>
              </div>

              {/* Tour Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Tour Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Tour (if applicable)</Label>
                    <Select 
                      value={formData.tourId || ''} 
                      onValueChange={(value) => updateFormData('tourId', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a tour" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific tour</SelectItem>
                        {tours.map((tour) => (
                          <SelectItem key={tour._id} value={tour._id}>
                            {tour.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="travelDate">Travel Date *</Label>
                    <DatePicker
                      date={formData.travelDate}
                      onDateChange={(date) => updateFormData('travelDate', date)}
                      placeholder="When did you travel?"
                      className={errors.travelDate ? 'border-red-500' : ''}
                    />
                    {errors.travelDate && <p className="text-red-500 text-sm mt-1">{errors.travelDate}</p>}
                  </div>
                </div>

                {!formData.tourId && (
                  <div>
                    <Label htmlFor="tourTitle">Custom Tour/Experience</Label>
                    <Input
                      id="tourTitle"
                      value={formData.tourTitle || ''}
                      onChange={(e) => updateFormData('tourTitle', e.target.value)}
                      placeholder="Describe your tour or experience"
                    />
                  </div>
                )}
              </div>

              {/* Review */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Your Review</h3>
                
                <div>
                  <Label>Rating *</Label>
                  <div className="mt-2">
                    {renderStars(formData.rating, (rating) => updateFormData('rating', rating))}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {formData.rating === 5 && "Excellent!"}
                    {formData.rating === 4 && "Very Good"}
                    {formData.rating === 3 && "Good"}
                    {formData.rating === 2 && "Fair"}
                    {formData.rating === 1 && "Poor"}
                  </p>
                  {errors.rating && <p className="text-red-500 text-sm mt-1">{errors.rating}</p>}
                </div>

                <div>
                  <Label htmlFor="title">Review Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => updateFormData('title', e.target.value)}
                    placeholder="Give your review a title"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
                </div>

                <div>
                  <Label htmlFor="content">Your Experience *</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => updateFormData('content', e.target.value)}
                    placeholder="Tell us about your experience in Sri Lanka. What did you enjoy most? What would you recommend to other travelers?"
                    rows={6}
                    className={errors.content ? 'border-red-500' : ''}
                  />
                  {errors.content && <p className="text-red-500 text-sm mt-1">{errors.content}</p>}
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.content.length}/1000 characters
                  </p>
                </div>
              </div>

              {submitStatus === 'error' && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-800">{errorMessage}</p>
                </div>
              )}

              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="bg-emerald-600 hover:bg-emerald-700"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Submit Testimonial
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Privacy Notice */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>
            By submitting this testimonial, you agree to our{' '}
            <Link href="/privacy" className="text-emerald-600 hover:text-emerald-700">
              Privacy Policy
            </Link>{' '}
            and consent to us using your review for marketing purposes.
          </p>
        </div>
      </div>
    </div>
  );
}
